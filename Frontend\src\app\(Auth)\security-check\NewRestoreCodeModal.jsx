'use client';
import { <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';
import { useState } from 'react';
import '@/css/account/Security.scss';

const NewRestoreCodeModal = ({ show, handleClose, restoreCode }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(restoreCode);
    setCopied(true);
    setTimeout(() => setCopied(false), 3000);
  };

  const handleDownloadCSV = () => {
    const csvContent = `TradeReply Restoral Code\n${restoreCode}`;
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'restoral-code.csv';
    link.click();
    URL.revokeObjectURL(url);
  };

  return (
    <Modal
      show={show}
      onHide={handleClose}
      centered
      size="xl"
      contentClassName="custom-modal-content"
    >
      <div className="px-4 sm:px-6 py-6 sm:py-10 rounded-[15px] space-y-4 sm:space-y-6 max-w-full overflow-hidden">
        <h5 className="text-2xl sm:text-[32px] font-extrabold text-white text-left">
          Your New Restoral Code
        </h5>

        <div className="space-y-4">
          <p className="text-base sm:text-[20px] font-semibold text-white text-left">
            You've successfully accessed your account using your previous restoral code.
          </p>
          
          <p className="text-base sm:text-[20px] font-semibold text-white text-left">
            For your security, a new restoral code has been generated. The old one is no longer valid.
          </p>
          
          <p className="text-base sm:text-[20px] font-semibold text-white text-left">
            Store this code somewhere safe — we won't show it again.
          </p>
        </div>

        <div className="text-base sm:text-[20px] font-semibold text-white text-left">
          Your Restoral Code{' '}
          <span className="inline-block px-4 py-2 mt-2 sm:mt-0 bg-white/20 text-white font-semibold rounded-md tracking-wide break-words text-center w-full sm:w-auto">
            {restoreCode}
          </span>
        </div>

        <p
          className="text-[#00b7ff] cursor-pointer text-sm sm:text-[16px] font-semibold text-left hover:underline"
          onClick={handleDownloadCSV}
        >
          Download as CSV
        </p>

        {copied && (
          <div className="green_text text-left text-sm sm:text-base">
            Restoral Code copied. Store it securely.
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mt-4">
          <Button
            onClick={handleCopy}
            className="!bg-[#B4B4B4] text-black font-semibold !rounded-md w-full sm:w-[200px]"
          >
            Copy Code
          </Button>
          <Button
            onClick={handleClose}
            className="bg-[#00b7ff] text-white font-semibold !rounded-md w-full sm:w-[200px]"
          >
            Done
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default NewRestoreCodeModal;

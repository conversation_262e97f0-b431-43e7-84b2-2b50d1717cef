{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/assets/svgIcons/SvgIcon.js"], "sourcesContent": ["export const EyeIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"12\"\r\n      viewBox=\"0 0 18 12\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M9 9.70508C9.9375 9.70508 10.7345 9.37708 11.391 8.72108C12.047 8.06458 12.375 7.26758 12.375 6.33008C12.375 5.39258 12.047 4.59558 11.391 3.93908C10.7345 3.28308 9.9375 2.95508 9 2.95508C8.0625 2.95508 7.2655 3.28308 6.609 3.93908C5.953 4.59558 5.625 5.39258 5.625 6.33008C5.625 7.26758 5.953 8.06458 6.609 8.72108C7.2655 9.37708 8.0625 9.70508 9 9.70508ZM9 8.35508C8.4375 8.35508 7.9595 8.15808 7.566 7.76408C7.172 7.37058 6.975 6.89258 6.975 6.33008C6.975 5.76758 7.172 5.28933 7.566 4.89533C7.9595 4.50183 8.4375 4.30508 9 4.30508C9.5625 4.30508 10.0408 4.50183 10.4347 4.89533C10.8282 5.28933 11.025 5.76758 11.025 6.33008C11.025 6.89258 10.8282 7.37058 10.4347 7.76408C10.0408 8.15808 9.5625 8.35508 9 8.35508ZM9 11.9551C7.175 11.9551 5.5125 11.4456 4.0125 10.4266C2.5125 9.40808 1.425 8.04258 0.75 6.33008C1.425 4.61758 2.5125 3.25183 4.0125 2.23283C5.5125 1.21433 7.175 0.705078 9 0.705078C10.825 0.705078 12.4875 1.21433 13.9875 2.23283C15.4875 3.25183 16.575 4.61758 17.25 6.33008C16.575 8.04258 15.4875 9.40808 13.9875 10.4266C12.4875 11.4456 10.825 11.9551 9 11.9551ZM9 10.4551C10.4125 10.4551 11.7095 10.0831 12.891 9.33908C14.072 8.59558 14.975 7.59258 15.6 6.33008C14.975 5.06758 14.072 4.06433 12.891 3.32033C11.7095 2.57683 10.4125 2.20508 9 2.20508C7.5875 2.20508 6.2905 2.57683 5.109 3.32033C3.928 4.06433 3.025 5.06758 2.4 6.33008C3.025 7.59258 3.928 8.59558 5.109 9.33908C6.2905 10.0831 7.5875 10.4551 9 10.4551Z\"\r\n        fill=\"#101014\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const CloseEye = () => (\r\n  <svg\r\n    xmlns=\"http://www.w3.org/2000/svg\"\r\n    width=\"22.016\"\r\n    height=\"17.613\"\r\n    viewBox=\"0 0 22.016 17.613\"\r\n  >\r\n    <path\r\n      id=\"Icon_awesome-eye-slash\"\r\n      data-name=\"Icon awesome-eye-slash\"\r\n      d=\"M11.008,13.76A4.935,4.935,0,0,1,6.092,9.181L2.484,6.392A11.465,11.465,0,0,0,1.221,8.3a1.113,1.113,0,0,0,0,1,11.033,11.033,0,0,0,9.787,6.1,10.685,10.685,0,0,0,2.679-.36L11.9,13.67a4.958,4.958,0,0,1-.894.09Zm10.8,2L18,12.819A11.4,11.4,0,0,0,20.8,9.308a1.113,1.113,0,0,0,0-1,11.033,11.033,0,0,0-9.787-6.1A10.6,10.6,0,0,0,5.94,3.5L1.564.116a.55.55,0,0,0-.773.1l-.675.869a.55.55,0,0,0,.1.772L20.452,17.5a.55.55,0,0,0,.773-.1l.676-.869a.55.55,0,0,0-.1-.772Zm-6.32-4.885L14.131,9.829a3.26,3.26,0,0,0-3.994-4.194,1.639,1.639,0,0,1,.32.97,1.6,1.6,0,0,1-.053.344L7.872,4.992a4.9,4.9,0,0,1,3.136-1.139,4.951,4.951,0,0,1,4.954,4.954,4.836,4.836,0,0,1-.478,2.068Z\"\r\n      transform=\"translate(0 0)\"\r\n      fill=\"#fff\"\r\n    />\r\n  </svg>\r\n);\r\nexport const ThemeIcon = () => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      width=\"32\"\r\n      height=\"32\"\r\n      viewBox=\"0 0 32 32\"\r\n    >\r\n      <g\r\n        id=\"Group_175598\"\r\n        data-name=\"Group 175598\"\r\n        transform=\"translate(-1847 -26)\"\r\n      >\r\n        <g\r\n          id=\"Group_175597\"\r\n          data-name=\"Group 175597\"\r\n          transform=\"translate(1842.007 28.533)\"\r\n        >\r\n          <rect\r\n            id=\"Rectangle_13566\"\r\n            data-name=\"Rectangle 13566\"\r\n            width=\"32\"\r\n            height=\"32\"\r\n            rx=\"16\"\r\n            transform=\"translate(4.993 -2.533)\"\r\n            fill=\"#f45126\"\r\n          />\r\n          <g\r\n            id=\"Group_175601\"\r\n            data-name=\"Group 175601\"\r\n            transform=\"translate(6.923 -0.604)\"\r\n          >\r\n            <path\r\n              id=\"Path_113806\"\r\n              data-name=\"Path 113806\"\r\n              d=\"M41.464,28.649a4.427,4.427,0,0,1-.409.578.185.185,0,1,0,.283.237,4.839,4.839,0,0,0,.444-.625.185.185,0,0,0-.317-.189Zm.521-1.312a4.5,4.5,0,0,1-.208.677.185.185,0,0,0,.343.136,4.837,4.837,0,0,0,.225-.733.184.184,0,1,0-.36-.08Zm.086-1.409a4.537,4.537,0,0,1,.012.708.184.184,0,1,0,.368.023,4.831,4.831,0,0,0-.013-.766.185.185,0,1,0-.367.035Zm-.355-1.366a4.469,4.469,0,0,1,.231.669.185.185,0,0,0,.357-.093,4.833,4.833,0,0,0-.251-.724.184.184,0,1,0-.338.148Zm-.764-1.187a4.508,4.508,0,0,1,.43.563.185.185,0,1,0,.31-.2,4.786,4.786,0,0,0-.465-.609.185.185,0,1,0-.275.246Z\"\r\n              transform=\"translate(-23.867 -12.612)\"\r\n              fill=\"#fff\"\r\n              fillRule=\"evenodd\"\r\n            />\r\n            <path\r\n              id=\"Path_113807\"\r\n              data-name=\"Path 113807\"\r\n              d=\"M13.748,7.475a6.273,6.273,0,1,0,6.273,6.273A6.276,6.276,0,0,0,13.748,7.475Zm.369.75a5.535,5.535,0,0,1,0,11.046ZM12.7,4.095v1.7a1.048,1.048,0,0,0,2.1,0v-1.7a1.048,1.048,0,0,0-2.1,0Zm7.133,2.087-1.2,1.2a1.048,1.048,0,0,0,1.482,1.482l1.2-1.2a1.048,1.048,0,1,0-1.482-1.482ZM23.4,12.7h-1.7a1.048,1.048,0,0,0,0,2.1h1.7a1.048,1.048,0,0,0,0-2.1Zm-2.087,7.133-1.2-1.2a1.048,1.048,0,0,0-1.482,1.482l1.2,1.2a1.048,1.048,0,1,0,1.482-1.482ZM14.8,23.4v-1.7a1.048,1.048,0,0,0-2.1,0v1.7a1.048,1.048,0,0,0,2.1,0ZM7.663,21.315l1.2-1.2a1.048,1.048,0,0,0-1.482-1.482l-1.2,1.2a1.048,1.048,0,1,0,1.482,1.482ZM4.095,14.8h1.7a1.048,1.048,0,0,0,0-2.1h-1.7a1.048,1.048,0,0,0,0,2.1ZM6.181,7.663l1.2,1.2A1.048,1.048,0,0,0,8.863,7.381l-1.2-1.2A1.048,1.048,0,0,0,6.181,7.663Z\"\r\n              fill=\"#fff\"\r\n              fillRule=\"evenodd\"\r\n            />\r\n          </g>\r\n        </g>\r\n      </g>\r\n    </svg>\r\n  );\r\n};\r\nexport const GlobalIcons = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-black-global.svg\" alt=\"Global Icons\" />\r\n  );\r\n};\r\nexport const GlobalBlueIcons = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-blue-global.svg\" alt=\"Global Blue Icons\" />\r\n  );\r\n};\r\nexport const UserBlackIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-black.svg\" alt=\"User Black Icon\" />\r\n  );\r\n};\r\nexport const UserBluekIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-brand-blue.svg\" alt=\"User Blue Icon\" />\r\n  );\r\n};\r\nexport const UserSolidBlueIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-account.svg\" alt=\"User Solid Blue Icon\" />\r\n  );\r\n};\r\nexport const SearchIcons = ({ width = 18, height = 18 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-search.svg\" width={width} height={height} alt=\"Search Icon\" />\r\n  );\r\n};\r\nexport const RoketIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-rocket.svg\" alt=\"Rocket Icon\" />\r\n  );\r\n};\r\nexport const FilterIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-filter.svg\" alt=\"Filter Icon\" />\r\n  );\r\n};\r\nexport const DashboardIcon = ({ color }) => {\r\n  return (\r\n    <img\r\n      src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradreply-dashboard.svg\"\r\n      alt=\"Dashboard Icon\"\r\n      className={color}\r\n    />\r\n  );\r\n};\r\nexport const DynamicIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-dynamic.svg\" alt=\"Dynamic Icon\" />\r\n  );\r\n};\r\nexport const KpiIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-kpi.svg\" alt=\"KPI Icon\" />\r\n  );\r\n};\r\nexport const GraphsIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradreply-graphs.svg\" alt=\"Graph Icon\" />\r\n  );\r\n};\r\nexport const ChartIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-charts.svg\" alt=\"Chart Icon\" />\r\n  );\r\n};\r\nexport const TrendIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-trend.svg\" alt=\"Trend Icon\" />\r\n  );\r\n};\r\nexport const RealTimeIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-real-time.svg\" alt=\"Real Time Icon\" />\r\n  );\r\n};\r\nexport const BrushIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-customize.svg\" alt=\"Brush Icon\" />\r\n  );\r\n};\r\nexport const LearningIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-learning.svg\" alt=\"Learning Icon\" />\r\n  );\r\n};\r\nexport const NextArrowIcon = () => {\r\n  return (\r\n    // <svg\r\n    //   width=\"26\"\r\n    //   height=\"22\"\r\n    //   viewBox=\"0 0 26 22\"\r\n    //   fill=\"none\"\r\n    //   xmlns=\"http://www.w3.org/2000/svg\"\r\n    // >\r\n    //   <path\r\n    //     d=\"M13.9387 21.1081C13.7989 20.9687 13.6879 20.8031 13.6122 20.6208C13.5365 20.4385 13.4975 20.243 13.4975 20.0456C13.4975 19.8481 13.5365 19.6527 13.6122 19.4703C13.6879 19.288 13.7989 19.1224 13.9387 18.9831L20.3749 12.5468L1.99995 12.5468C1.60212 12.5468 1.2206 12.3888 0.93929 12.1075C0.657985 11.8262 0.49995 11.4446 0.49995 11.0468C0.49995 10.649 0.657985 10.2675 0.939291 9.98616C1.2206 9.70485 1.60212 9.54682 1.99995 9.54682L20.3749 9.54682L13.9387 3.10807C13.6569 2.82628 13.4986 2.44409 13.4986 2.04557C13.4986 1.64706 13.6569 1.26486 13.9387 0.98307C14.2205 0.701278 14.6027 0.542968 15.0012 0.542968C15.3997 0.542968 15.7819 0.701278 16.0637 0.98307L25.0637 9.98307C25.2035 10.1224 25.3145 10.288 25.3902 10.4703C25.4659 10.6527 25.5049 10.8481 25.5049 11.0456C25.5049 11.243 25.4659 11.4385 25.3902 11.6208C25.3145 11.8031 25.2035 11.9687 25.0637 12.1081L16.0637 21.1081C15.9243 21.2479 15.7588 21.3589 15.5764 21.4346C15.3941 21.5103 15.1986 21.5493 15.0012 21.5493C14.8038 21.5493 14.6083 21.5103 14.426 21.4346C14.2436 21.3589 14.0781 21.2479 13.9387 21.1081Z\"\r\n    //     fill=\"white\"\r\n    //   />\r\n    // </svg>\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-next-arrow.svg\" alt=\"Next Arrow Icon\" />\r\n  );\r\n};\r\nexport const PrevIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-prev-arrow.svg\" alt=\"Prev Icon\" />\r\n  );\r\n};\r\nexport const QuoteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-quote.svg\" alt=\"Quote Icon\"\r\n      width=\"31\"\r\n      height=\"26\"\r\n    />\r\n  );\r\n};\r\nexport const CheckIcon = ({ height = 28, width = 28 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-success.svg\" alt=\"Check Icon\"\r\n      width={width}\r\n      height={height}\r\n    />\r\n  );\r\n};\r\nexport const RedCrossIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-red-x.svg\" alt=\"Red Cross Icon\" />\r\n  );\r\n};\r\nexport const PlusIcon = ({ color, height = 32, width = 33 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-plus.svg\" alt=\"Plus Icon\"\r\n      width={width}\r\n      height={height}\r\n      className={color}\r\n    />\r\n  );\r\n};\r\nexport const MinusIcon = ({ width = '29px', height = '4px' }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-minus.svg\" height={height} width={width} alt=\"Minus Icon\" />\r\n  );\r\n};\r\nexport const RedInfoStarIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-red-warning-star.svg\" alt=\"Red Info Star Icon\" />\r\n  );\r\n};\r\nexport const GreenCheckStarIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-green-check-star.svg\" alt=\"Green Check Star\"\r\n      width=\"42\"\r\n      height=\"42\"\r\n    />\r\n  );\r\n};\r\nexport const NoteSettingBlueIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-note-setting.svg\" alt=\"Note Setting Blue Icon\" />\r\n  );\r\n};\r\nexport const SettingIcon = () => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      width=\"38\"\r\n      height=\"40\"\r\n      viewBox=\"0 0 38 40\"\r\n      fill=\"none\">\r\n      <path\r\n        d=\"M19.0264 0.5C20.4944 0.516 21.9564 0.686 23.3904 1.006C23.6955 1.0741 23.9716 1.23581 24.1803 1.46852C24.389 1.70124 24.5198 1.99334 24.5544 2.304L24.8944 5.358C24.9424 5.78857 25.0908 6.20186 25.3277 6.56461C25.5645 6.92737 25.8832 7.22946 26.2581 7.44658C26.633 7.6637 27.0537 7.78979 27.4862 7.8147C27.9187 7.83961 28.351 7.76264 28.7484 7.59L31.5484 6.36C31.8329 6.2347 32.1493 6.20088 32.4538 6.26323C32.7584 6.32557 33.036 6.48099 33.2484 6.708C35.2725 8.87031 36.7803 11.4633 37.6584 14.292C37.7501 14.5895 37.7471 14.9081 37.6496 15.2038C37.5521 15.4994 37.3651 15.7574 37.1144 15.942L34.6324 17.774C34.2827 18.0303 33.9983 18.3655 33.8023 18.7522C33.6063 19.1389 33.5041 19.5664 33.5041 20C33.5041 20.4336 33.6063 20.8611 33.8023 21.2478C33.9983 21.6345 34.2827 21.9697 34.6324 22.226L37.1184 24.056C37.3695 24.2407 37.5568 24.499 37.6543 24.7951C37.7518 25.0912 37.7546 25.4102 37.6624 25.708C36.7849 28.5366 35.2778 31.1295 33.2544 33.292C33.0424 33.5189 32.7652 33.6745 32.4611 33.7372C32.1569 33.7999 31.8408 33.7666 31.5564 33.642L28.7444 32.408C28.3476 32.234 27.9154 32.1559 27.4827 32.18C27.0501 32.204 26.6292 32.3296 26.2542 32.5466C25.8791 32.7635 25.5604 33.0657 25.3238 33.4287C25.0872 33.7917 24.9394 34.2053 24.8924 34.636L24.5524 37.688C24.5184 37.995 24.3905 38.2841 24.1861 38.5157C23.9817 38.7473 23.7108 38.9101 23.4104 38.982C20.5136 39.6726 17.4951 39.6726 14.5984 38.982C14.2976 38.9105 14.0262 38.7478 13.8214 38.5162C13.6167 38.2845 13.4885 37.9953 13.4544 37.688L13.1164 34.64C13.0673 34.2106 12.9182 33.7987 12.6811 33.4374C12.4439 33.0761 12.1254 32.7754 11.751 32.5595C11.3766 32.3437 10.9568 32.2186 10.5253 32.1943C10.0938 32.1701 9.6626 32.2474 9.26638 32.42L6.45438 33.652C6.1697 33.7771 5.85318 33.8106 5.54862 33.7479C5.24406 33.6852 4.96652 33.5293 4.75438 33.302C2.73064 31.137 1.22419 28.5412 0.348384 25.71C0.256177 25.4122 0.259017 25.0932 0.35651 24.7971C0.454002 24.501 0.641304 24.2427 0.892384 24.058L3.37838 22.226C3.72808 21.9697 4.01246 21.6345 4.20848 21.2478C4.40451 20.8611 4.50666 20.4336 4.50666 20C4.50666 19.5664 4.40451 19.1389 4.20848 18.7522C4.01246 18.3655 3.72808 18.0303 3.37838 17.774L0.892384 15.946C0.641304 15.7613 0.454002 15.503 0.35651 15.2069C0.259017 14.9108 0.256177 14.5918 0.348384 14.294C1.22647 11.4653 2.73423 8.87231 4.75838 6.71C4.97075 6.48299 5.24841 6.32757 5.55296 6.26523C5.8575 6.20288 6.17389 6.2367 6.45838 6.362L9.25838 7.592C9.65642 7.76454 10.0894 7.84132 10.5225 7.81617C10.9556 7.79102 11.3767 7.66465 11.7521 7.44719C12.1275 7.22974 12.4467 6.92727 12.684 6.56408C12.9212 6.2009 13.07 5.78712 13.1184 5.356L13.4584 2.304C13.4927 1.99271 13.6236 1.69997 13.8327 1.46683C14.0418 1.23369 14.3186 1.07185 14.6244 1.004C16.0564 0.686667 17.5237 0.518667 19.0264 0.5ZM19.0024 14C17.4111 14 15.885 14.6321 14.7597 15.7574C13.6345 16.8826 13.0024 18.4087 13.0024 20C13.0024 21.5913 13.6345 23.1174 14.7597 24.2426C15.885 25.3679 17.4111 26 19.0024 26C20.5937 26 22.1198 25.3679 23.245 24.2426C24.3702 23.1174 25.0024 21.5913 25.0024 20C25.0024 18.4087 24.3702 16.8826 23.245 15.7574C22.1198 14.6321 20.5937 14 19.0024 14Z\"\r\n        fill=\"#FEA500\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const SolidSettingIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-yellow-wrench.svg\" alt=\"Solid Setting Icon\" />\r\n  );\r\n};\r\nexport const RightSolidArrowIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-right-solid-arrow.svg\" alt=\"\" />\r\n  );\r\n};\r\nexport const SolidInfoIcon = ({ width = '20px', height = '20px' }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-info-marker.svg\" height={height} width={width} alt=\"Info Marker Icon\" />\r\n  );\r\n};\r\nexport const WhiteInfoIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-info-white.svg\" alt=\"White Info Marker Icon\" />\r\n  );\r\n};\r\nexport const RightArrowIcon = ({ color }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-right-arrow.svg\" alt=\"Right Arrow Icon\"\r\n      className={color}\r\n    />\r\n  );\r\n};\r\nexport const CartIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"25\"\r\n      height=\"20\"\r\n      viewBox=\"0 0 25 20\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M20.8333 16C19.3533 16 18.1667 16.89 18.1667 18C18.1667 18.5304 18.4476 19.0391 18.9477 19.4142C19.4478 19.7893 20.1261 20 20.8333 20C21.5406 20 22.2189 19.7893 22.719 19.4142C23.219 19.0391 23.5 18.5304 23.5 18C23.5 17.4696 23.219 16.9609 22.719 16.5858C22.2189 16.2107 21.5406 16 20.8333 16ZM-0.5 0V2H2.16667L6.96667 9.59L5.15333 12.04C4.95333 12.32 4.83333 12.65 4.83333 13C4.83333 13.5304 5.11428 14.0391 5.61438 14.4142C6.11448 14.7893 6.79276 15 7.5 15H23.5V13H8.06C7.97159 13 7.88681 12.9737 7.8243 12.9268C7.76178 12.8799 7.72667 12.8163 7.72667 12.75C7.72667 12.7 7.74 12.66 7.76667 12.63L8.96667 11H18.9C19.9 11 20.78 10.58 21.2333 9.97L26.0067 3.5C26.1 3.34 26.1667 3.17 26.1667 3C26.1667 2.73478 26.0262 2.48043 25.7761 2.29289C25.5261 2.10536 25.187 2 24.8333 2H5.11333L3.86 0M7.5 16C6.02 16 4.83333 16.89 4.83333 18C4.83333 18.5304 5.11428 19.0391 5.61438 19.4142C6.11448 19.7893 6.79276 20 7.5 20C8.20724 20 8.88552 19.7893 9.38562 19.4142C9.88571 19.0391 10.1667 18.5304 10.1667 18C10.1667 17.4696 9.88571 16.9609 9.38562 16.5858C8.88552 16.2107 8.20724 16 7.5 16Z\"\r\n        fill=\"white\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const SignoutIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-signout.svg\" alt=\"Signout Icon\" />\r\n  );\r\n};\r\nexport const HelpIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradreply-helpicon.svg\" alt=\"Help Icon\" />\r\n  );\r\n};\r\nexport const BaseEyeIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye.svg\" alt=\"Base Eye Icon\" />\r\n  );\r\n};\r\nexport const UserBlueIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-blue.svg\" alt=\"User Blue Icon\" />\r\n  );\r\n};\r\nexport const DollerIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-renew-dollar.svg\" alt=\"Dollar Icon\" />\r\n  );\r\n};\r\nexport const SecurityIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-security.svg\" alt=\"Security Icon\" />\r\n  );\r\n};\r\nexport const LockIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-lock.svg\" alt=\"Lock Icon\" />\r\n  );\r\n};\r\nexport const LinkIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-links.svg\" alt=\"Link Icon\" />\r\n  );\r\n};\r\nexport const PaymentIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-payment.svg\" alt=\"Payment Icon\" />\r\n  );\r\n};\r\nexport const PaymentIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"20\"\r\n      height=\"17\"\r\n      viewBox=\"0 0 20 17\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M18 0.5H2C0.89 0.5 0.00999999 1.39 0.00999999 2.5L0 14.5C0 15.61 0.89 16.5 2 16.5H18C19.11 16.5 20 15.61 20 14.5V2.5C20 1.39 19.11 0.5 18 0.5ZM18 14.5H2V8.5H18V14.5ZM18 4.5H2V2.5H18V4.5Z\"\r\n        fill=\"#00ADEF\">\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const CartSideIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-carticon.svg\" alt=\"Cart Icon\" />\r\n  );\r\n};\r\nexport const EditIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-editcon.svg\" alt=\"Edit Icon\" />\r\n  );\r\n};\r\n\r\nexport const CrossIcon = ({ color }) => {\r\n  return (\r\n    // <svg\r\n    //   width=\"18\"\r\n    //   height=\"19\"\r\n    //   viewBox=\"0 0 18 19\"\r\n    //   fill=\"none\"\r\n    //   xmlns=\"http://www.w3.org/2000/svg\"\r\n    // >\r\n    //   <path\r\n    //     d=\"M9 0.75C4.125 0.75 0.25 4.625 0.25 9.5C0.25 14.375 4.125 18.25 9 18.25C13.875 18.25 17.75 14.375 17.75 9.5C17.75 4.625 13.875 0.75 9 0.75ZM12.375 13.875L9 10.5L5.625 13.875L4.625 12.875L8 9.5L4.625 6.125L5.625 5.125L9 8.5L12.375 5.125L13.375 6.125L10 9.5L13.375 12.875L12.375 13.875Z\"\r\n    //     fill=\"white\"\r\n    //   />\r\n    // </svg>\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-white-x.svg\" alt=\"White Cirle Cross Icon\"\r\n      width=\"18\"\r\n      height=\"19\"\r\n\r\n      className={color}\r\n    />\r\n  );\r\n};\r\nexport const PublicProfileIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-profile-user-light.svg\" alt=\"Public Profile Icon\" />\r\n  );\r\n};\r\nexport const SellerDashboardIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-gauge.svg\" alt=\"Seller Dashboard Icon\" />\r\n  );\r\n};\r\nexport const MarketplaceDisputeIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-gavel.svg\" alt=\"Marketplace Dispute Icon\" />\r\n  );\r\n};\r\nexport const MarketplaceListIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-marketplace-icon.svg\" alt=\"Marketplace List Icon\" />\r\n  );\r\n};\r\nexport const PurchasedProductIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-package.svg\" alt=\"Purchased Product Icon\" />\r\n  );\r\n};\r\nexport const SoldProductIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-coin.svg\" alt=\"Sold Product Icon\" />\r\n  );\r\n};\r\nexport const LogoutIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"18\"\r\n      viewBox=\"0 0 18 18\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M2 18C1.45 18 0.979333 17.8043 0.588 17.413C0.196667 17.0217 0.000666667 16.5507 0 16V2C0 1.45 0.196 0.979333 0.588 0.588C0.98 0.196667 1.45067 0.000666667 2 0H9V2H2V16H9V18H2ZM13 14L11.625 12.55L14.175 10H6V8H14.175L11.625 5.45L13 4L18 9L13 14Z\"\r\n        fill=\"#00ADEF\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const CheckGradientIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"211\"\r\n      height=\"208\"\r\n      viewBox=\"0 0 211 208\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M203.341 74.5632C206.051 83.8216 207.504 93.6158 207.504 103.752C207.504 161.052 161.052 207.504 103.752 207.504C46.4519 207.504 0 161.052 0 103.752C0 46.4518 46.4519 -9.15527e-05 103.752 -9.15527e-05C131.892 -9.15527e-05 157.416 11.2021 176.105 29.3936L194.876 22.6914L101.952 115.617L63.9188 77.5842L39.9479 101.556L101.952 163.559L210.127 55.3835L203.341 74.5632Z\"\r\n        fill=\"url(#paint0_radial_490_1337)\"\r\n      />\r\n      <defs>\r\n        <radialGradient\r\n          id=\"paint0_radial_490_1337\"\r\n          cx=\"0\"\r\n          cy=\"0\"\r\n          r=\"1\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n          gradientTransform=\"translate(59.8666 54.0097) rotate(45) scale(197.976)\"\r\n        >\r\n          <stop stopColor=\"#73D1E1\" />\r\n          <stop offset=\"1\" stopColor=\"#395BB2\" />\r\n        </radialGradient>\r\n      </defs>\r\n    </svg>\r\n  );\r\n};\r\nexport const CalculatorIcon = () => {\r\n  return (\r\n    // <svg\r\n    //   width=\"38\"\r\n    //   height=\"38\"\r\n    //   viewBox=\"0 0 38 38\"\r\n    //   fill=\"none\"\r\n    //   xmlns=\"http://www.w3.org/2000/svg\"\r\n    // >\r\n    //   <path\r\n    //     d=\"M10.6667 31.5H13.7917V27.3333H17.9583V24.2083H13.7917V20.0417H10.6667V24.2083H6.5V27.3333H10.6667V31.5ZM21.0833 29.9375H31.5V26.8125H21.0833V29.9375ZM21.0833 24.7292H31.5V21.6042H21.0833V24.7292ZM23.375 16.8125L26.2917 13.8958L29.2083 16.8125L31.3958 14.625L28.4792 11.6042L31.3958 8.6875L29.2083 6.5L26.2917 9.41667L23.375 6.5L21.1875 8.6875L24.1042 11.6042L21.1875 14.625L23.375 16.8125ZM7.02084 13.1667H17.4375V10.0417H7.02084V13.1667ZM4.41667 37.75C3.27084 37.75 2.28959 37.3424 1.47292 36.5271C0.656253 35.7118 0.248615 34.7306 0.250004 33.5833V4.41667C0.250004 3.27083 0.658337 2.29028 1.475 1.475C2.29167 0.659722 3.27223 0.251389 4.41667 0.25H33.5833C34.7292 0.25 35.7104 0.658333 36.5271 1.475C37.3438 2.29167 37.7514 3.27222 37.75 4.41667V33.5833C37.75 34.7292 37.3424 35.7104 36.5271 36.5271C35.7118 37.3438 34.7306 37.7514 33.5833 37.75H4.41667Z\"\r\n    //     fill=\"#00ADEF\"\r\n    //   />\r\n    // </svg>\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-calculator.svg\" alt=\"\" />\r\n  );\r\n};\r\nexport const SolidRedArrowIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"31\"\r\n      height=\"36\"\r\n      viewBox=\"0 0 31 36\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M28.5 13.6699C31.8333 15.5944 31.8333 20.4056 28.5 22.3301L7.5 34.4545C4.16666 36.379 -1.74729e-06 33.9734 -1.57905e-06 30.1243L-5.19101e-07 5.87564C-3.50856e-07 2.02664 4.16667 -0.378984 7.5 1.54552L28.5 13.6699Z\"\r\n        fill=\"#FF696A\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const DropArrowIcon = ({ width = 24, height = 13 }) => {\r\n  return (\r\n    <svg\r\n      width={width}\r\n      height={height}\r\n      viewBox=\"0 0 24 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M3.47599 3.12261e-08L0.857422 2.47368L12.0003 13L23.1431 2.47368L20.5246 2.34528e-07L12.0003 8.03509L3.47599 3.12261e-08Z\"\r\n        fill=\"white\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const DropArrowUpIcon = ({ width = 24, height = 13 }) => {\r\n  return (\r\n    <svg\r\n      width={width}\r\n      height={height}\r\n      viewBox=\"0 0 24 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      style={{ transform: 'rotate(180deg)' }}\r\n    >\r\n      <path\r\n        d=\"M3.47599 3.12261e-08L0.857422 2.47368L12.0003 13L23.1431 2.47368L20.5246 2.34528e-07L12.0003 8.03509L3.47599 3.12261e-08Z\"\r\n        fill=\"white\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const DropDownArrowIcon = ({ width = 14, height = 7 }) => {\r\n  return (\r\n    <svg width={width}\r\n      height={height}\r\n      viewBox=\"0 0 14 7\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\">\r\n      <path d=\"M0.333008 0.333984L6.99967 7.00065L13.6663 0.333984H0.333008Z\" fill=\"white\" />\r\n    </svg>\r\n\r\n  );\r\n};\r\nexport const BlackDropDownArrowIcon = ({ width = 14, height = 7 }) => {\r\n  return (\r\n    <svg width={width}\r\n      height={height}\r\n      viewBox=\"0 0 14 7\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <path d=\"M0.333252 0.333008L6.99992 6.99967L13.6666 0.333008H0.333252Z\" fill=\"#666666\" />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const DeleteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-delete.svg\" alt=\"Delete Icon\"\r\n      width=\"20\"\r\n      height=\"21\"\r\n    />\r\n  );\r\n};\r\nexport const TradeIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"48\"\r\n      height=\"48\"\r\n      viewBox=\"0 0 48 48\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M0.75 42.0833H47.25V47.25H0.75V42.0833ZM8.5 34.3333C11.3417 34.3333 13.6667 32.0083 13.6667 29.1667C13.6667 27.875 13.15 26.5833 12.375 25.8083L15.7333 18.8333H16.25C17.5417 18.8333 18.8333 18.3167 19.6083 17.5417L26.5833 21.1583V21.4167C26.5833 24.2583 28.9083 26.5833 31.75 26.5833C34.5917 26.5833 36.9167 24.2583 36.9167 21.4167C36.9167 20.125 36.4 19.0917 35.625 18.0583L38.9833 11.0833H39.5C42.3417 11.0833 44.6667 8.75833 44.6667 5.91667C44.6667 3.075 42.3417 0.75 39.5 0.75C36.6583 0.75 34.3333 3.075 34.3333 5.91667C34.3333 7.20833 34.85 8.5 35.625 9.275L32.2667 16.25H31.75C30.4583 16.25 29.1667 16.7667 28.3917 17.5417L21.4167 14.1833V13.6667C21.4167 10.825 19.0917 8.5 16.25 8.5C13.4083 8.5 11.0833 10.825 11.0833 13.6667C11.0833 14.9583 11.6 16.25 12.375 17.025L9.01667 24H8.5C5.65833 24 3.33333 26.325 3.33333 29.1667C3.33333 32.0083 5.65833 34.3333 8.5 34.3333Z\"\r\n        fill=\"#00ADEF\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const ArtArrowIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"30\"\r\n      height=\"32\"\r\n      viewBox=\"0 0 30 32\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M1.64673 22.9062L27.6275 7.90625\"\r\n        stroke=\"#00ADEF\"\r\n        strokeWidth=\"3\"\r\n        strokeLinecap=\"round\"\r\n      />\r\n      <path\r\n        d=\"M1.64673 15.0469L8.64673 8.04688\"\r\n        stroke=\"#00ADEF\"\r\n        strokeWidth=\"3\"\r\n        strokeLinecap=\"round\"\r\n      />\r\n      <path\r\n        d=\"M3.14673 29.0469H13.0462\"\r\n        stroke=\"#00ADEF\"\r\n        strokeWidth=\"3\"\r\n        strokeLinecap=\"round\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const RedInfoIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-warning.svg\" alt=\"Red Info Icon\"\r\n      width=\"28\"\r\n      height=\"28\"\r\n    />\r\n  );\r\n};\r\nexport const GreyCheckIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-success-gray.svg\" alt=\"Grey Success Icon\"\r\n      width=\"28\"\r\n      height=\"28\"\r\n    />\r\n  );\r\n};\r\nexport const GreyCrossIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-failure.svg\" alt=\"Grey Cross Icon\"\r\n      width=\"28\"\r\n      height=\"28\"\r\n    />\r\n  );\r\n};\r\nexport const ReferIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-refer-a-friend.svg\" alt=\"Refer Icon\" />\r\n  );\r\n};\r\nexport const PartnershipIcon = () => {\r\n  return (\r\n    <svg\r\n      id=\"Layer_1\"\r\n      data-name=\"Layer 1\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      width=\"20\"\r\n      height=\"20\"\r\n      viewBox=\"0 0 24 24\"\r\n    >\r\n      <path\r\n        d=\"m17.063,2.185c-1.245.06-2.442.603-3.367,1.528l-3.63,3.63c.57-.573,2.687-.179,3.2.334l2.197-2.197c.487-.487,1.096-.785,1.719-.812.424-.021,1.024.069,1.552.597.493.493.597,1.066.597,1.457,0,.654-.299,1.304-.812,1.815l-3.821,3.845c-.961.961-2.424,1.039-3.272.191-.484-.484-1.281-.487-1.767,0s-.487,1.281,0,1.767c.872.872,2.018,1.313,3.2,1.313,1.278,0,2.582-.522,3.582-1.528l3.845-3.821c.976-.973,1.528-2.275,1.528-3.582,0-1.215-.46-2.37-1.313-3.224-.913-.913-2.14-1.373-3.439-1.313Zm-5.922,6.161c-1.278,0-2.603.525-3.606,1.528l-3.821,3.821c-.976.973-1.528,2.275-1.528,3.582,0,1.215.46,2.37,1.313,3.224.913.913,2.14,1.373,3.439,1.313,1.245-.06,2.442-.603,3.367-1.528l3.63-3.63c-.573.573-2.687.179-3.2-.334l-2.197,2.197c-.487.487-1.096.782-1.719.812-.424.021-1.024-.069-1.552-.597-.493-.493-.597-1.069-.597-1.457,0-.654.299-1.304.812-1.815l3.821-3.845c.961-.961,2.424-1.036,3.272-.191.487.487,1.284.487,1.767,0,.487-.487.487-1.281,0-1.767-.872-.872-2.021-1.313-3.2-1.313Z\"\r\n        fill=\"#fff\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const ResendCodeIcon = ({ isRotating }) => {\r\n  return (\r\n    <svg\r\n      className={isRotating ? \"rotate\" : \"\"}\r\n      width=\"19\"\r\n      height=\"18\"\r\n      viewBox=\"0 0 19 18\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M18.7693 1.84688V6.34688C18.7693 6.54579 18.6903 6.73656 18.5497 6.87721C18.409 7.01786 18.2182 7.09688 18.0193 7.09688H13.5193C13.3717 7.09664 13.2275 7.05285 13.1047 6.97101C12.9818 6.88917 12.8859 6.77291 12.8289 6.63679C12.7718 6.50066 12.7562 6.35074 12.784 6.20578C12.8117 6.06082 12.8816 5.92728 12.985 5.82188L14.71 4.09688L14.3068 3.69375C13.2576 2.64569 11.9212 1.93222 10.4666 1.6435C9.01196 1.35479 7.50438 1.5038 6.13442 2.07171C4.76446 2.63961 3.59362 3.60091 2.76988 4.83409C1.94613 6.06728 1.50648 7.517 1.50648 9C1.50648 10.483 1.94613 11.9327 2.76988 13.1659C3.59362 14.3991 4.76446 15.3604 6.13442 15.9283C7.50438 16.4962 9.01196 16.6452 10.4666 16.3565C11.9212 16.0678 13.2576 15.3543 14.3068 14.3063C14.3758 14.2357 14.4582 14.1796 14.5492 14.1413C14.6401 14.103 14.7378 14.0833 14.8365 14.0833C14.9352 14.0833 15.0329 14.103 15.1239 14.1413C15.2148 14.1796 15.2972 14.2357 15.3662 14.3063C15.5065 14.4469 15.5852 14.6373 15.5852 14.8359C15.5852 15.0345 15.5065 15.225 15.3662 15.3656C14.1073 16.6238 12.5037 17.4805 10.758 17.8274C9.01229 18.1743 7.20293 17.9958 5.55867 17.3145C3.91441 16.6331 2.50908 15.4796 1.52035 13.9996C0.531628 12.5197 0.00390625 10.7798 0.00390625 9C0.00390625 7.22017 0.531628 5.4803 1.52035 4.00036C2.50908 2.52042 3.91441 1.36687 5.55867 0.685539C7.20293 0.00421169 9.01229 -0.174293 10.758 0.172592C12.5037 0.519478 14.1073 1.37618 15.3662 2.63438L15.7693 3.0375L17.485 1.32188C17.589 1.21629 17.722 1.14391 17.8672 1.11388C18.0124 1.08384 18.1632 1.0975 18.3006 1.15313C18.4374 1.21109 18.5545 1.30747 18.6377 1.43059C18.7209 1.55371 18.7666 1.69831 18.7693 1.84688Z\"\r\n        fill=\"white\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const ContactCustomerSupport = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"18\"\r\n      viewBox=\"0 0 18 18\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M9 0.875C7.39303 0.875 5.82214 1.35152 4.486 2.24431C3.14985 3.1371 2.10844 4.40605 1.49348 5.8907C0.87852 7.37535 0.717618 9.00901 1.03112 10.5851C1.34463 12.1612 2.11846 13.6089 3.25476 14.7452C4.39106 15.8815 5.8388 16.6554 7.4149 16.9689C8.99099 17.2824 10.6247 17.1215 12.1093 16.5065C13.594 15.8916 14.8629 14.8502 15.7557 13.514C16.6485 12.1779 17.125 10.607 17.125 9C17.1209 6.84638 16.2635 4.78216 14.7407 3.25932C13.2178 1.73648 11.1536 0.87913 9 0.875ZM9 14C8.81458 14 8.63333 13.945 8.47916 13.842C8.32499 13.739 8.20482 13.5926 8.13387 13.4213C8.06291 13.25 8.04434 13.0615 8.08052 12.8796C8.11669 12.6977 8.20598 12.5307 8.33709 12.3996C8.4682 12.2685 8.63525 12.1792 8.81711 12.143C8.99896 12.1068 9.18746 12.1254 9.35877 12.1964C9.53007 12.2673 9.67649 12.3875 9.77951 12.5417C9.88252 12.6958 9.9375 12.8771 9.9375 13.0625C9.9375 13.3111 9.83873 13.5496 9.66292 13.7254C9.4871 13.9012 9.24864 14 9 14ZM9.625 10.1797V10.25C9.625 10.4158 9.55916 10.5747 9.44195 10.6919C9.32474 10.8092 9.16576 10.875 9 10.875C8.83424 10.875 8.67527 10.8092 8.55806 10.6919C8.44085 10.5747 8.375 10.4158 8.375 10.25V9.625C8.375 9.45924 8.44085 9.30027 8.55806 9.18306C8.67527 9.06585 8.83424 9 9 9C9.30904 9 9.61113 8.90836 9.86808 8.73667C10.125 8.56498 10.3253 8.32095 10.4436 8.03544C10.5618 7.74993 10.5928 7.43577 10.5325 7.13267C10.4722 6.82958 10.3234 6.55117 10.1049 6.33265C9.88634 6.11413 9.60793 5.96531 9.30483 5.90502C9.00174 5.84473 8.68757 5.87568 8.40206 5.99394C8.11655 6.1122 7.87252 6.31247 7.70083 6.56942C7.52914 6.82637 7.4375 7.12847 7.4375 7.4375C7.4375 7.60326 7.37166 7.76223 7.25445 7.87944C7.13724 7.99665 6.97826 8.0625 6.8125 8.0625C6.64674 8.0625 6.48777 7.99665 6.37056 7.87944C6.25335 7.76223 6.1875 7.60326 6.1875 7.4375C6.18751 6.90805 6.33695 6.38936 6.61865 5.94107C6.90036 5.49279 7.30287 5.13312 7.77991 4.90344C8.25694 4.67376 8.78912 4.58339 9.31523 4.64273C9.84134 4.70207 10.34 4.90871 10.7539 5.23888C11.1678 5.56905 11.4801 6.00934 11.6549 6.50911C11.8296 7.00888 11.8598 7.54784 11.7418 8.06398C11.6239 8.58013 11.3627 9.05251 10.9882 9.42678C10.6137 9.80106 10.1412 10.062 9.625 10.1797Z\"\r\n        fill=\"#00ADEF\"\r\n      />\r\n    </svg>\r\n\r\n\r\n  );\r\n};\r\nexport const RedErrorCircle = () => {\r\n  return (\r\n    // <svg\r\n    //   xmlns=\"http://www.w3.org/2000/svg\"\r\n    //   width=\"24\"\r\n    //   height=\"24\"\r\n    //   viewBox=\"0 0 24 24\"\r\n    //   fill=\"#ff696a\"\r\n    // >\r\n    //   <path\r\n    //     d=\"M11.953 2C6.465 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.493 2 11.953 2zM13 17h-2v-2h2v2zm0-4h-2V7h2v6z\"\r\n    //     fill=\"#fffff\"\r\n    //   />\r\n    // </svg>\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-warning.svg\"\r\n      alt=\"Red Error Icon\"\r\n      width=\"24\"\r\n      height=\"24\"\r\n    />\r\n  );\r\n};\r\nexport const BlackErrorCircle = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"17\"\r\n      viewBox=\"0 0 18 17\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M9 0.375C9 0.375 10.6526 0.375 12.1628 1.01376C12.1628 1.01376 13.621 1.63053 14.7452 2.75476C14.7452 2.75476 15.8695 3.87899 16.4862 5.33719C16.4862 5.33719 17.125 6.84739 17.125 8.5C17.125 8.5 17.125 10.1526 16.4862 11.6628C16.4862 11.6628 15.8695 13.121 14.7452 14.2452C14.7452 14.2452 13.621 15.3695 12.1628 15.9862C12.1628 15.9862 10.6526 16.625 9 16.625C9 16.625 7.34739 16.625 5.83719 15.9862C5.83719 15.9862 4.37899 15.3695 3.25476 14.2452C3.25476 14.2452 2.13053 13.121 1.51376 11.6628C1.51376 11.6628 0.875 10.1526 0.875 8.5C0.875 8.5 0.875 6.84739 1.51376 5.33719C1.51376 5.33719 2.13053 3.87899 3.25476 2.75476C3.25476 2.75476 4.37899 1.63053 5.83719 1.01376C5.83719 1.01376 7.34739 0.375 9 0.375ZM9 1.625C9 1.625 7.60087 1.625 6.32413 2.16501C6.32413 2.16501 5.09047 2.68681 4.13864 3.63864C4.13864 3.63864 3.18681 4.59047 2.66502 5.82413C2.66502 5.82413 2.125 7.10087 2.125 8.5C2.125 8.5 2.125 9.89913 2.66502 11.1759C2.66502 11.1759 3.18681 12.4095 4.13864 13.3614C4.13864 13.3614 5.09047 14.3132 6.32413 14.835C6.32413 14.835 7.60087 15.375 9 15.375C9 15.375 10.3991 15.375 11.6759 14.835C11.6759 14.835 12.9095 14.3132 13.8614 13.3614C13.8614 13.3614 14.8132 12.4095 15.335 11.1759C15.335 11.1759 15.875 9.89912 15.875 8.5C15.875 8.5 15.875 7.10087 15.335 5.82413C15.335 5.82413 14.8132 4.59047 13.8614 3.63864C13.8614 3.63864 12.9095 2.68681 11.6759 2.16501C11.6759 2.16501 10.3991 1.625 9 1.625Z\"\r\n        fill=\"#1C1C1C\"\r\n      />\r\n      <path\r\n        d=\"M9 12.875H9.625C9.97018 12.875 10.25 12.5952 10.25 12.25C10.25 11.9048 9.97018 11.625 9.625 11.625V7.875C9.625 7.52982 9.34518 7.25 9 7.25H8.375C8.02982 7.25 7.75 7.52982 7.75 7.875C7.75 8.22018 8.02982 8.5 8.375 8.5V12.25C8.375 12.5952 8.65482 12.875 9 12.875Z\"\r\n        fill=\"#1C1C1C\"\r\n      />\r\n      <path\r\n        d=\"M9.78125 5.0625C9.78125 5.58027 9.36148 6 8.84375 6C8.32602 6 7.90625 5.58027 7.90625 5.0625C7.90625 4.54473 8.32602 4.125 8.84375 4.125C9.36148 4.125 9.78125 4.54473 9.78125 5.0625Z\"\r\n        fill=\"#1C1C1C\"\r\n      />\r\n    </svg>\r\n\r\n  );\r\n};\r\nexport const DeviceMobileSpeaker = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-device-00ADEF.svg\" alt=\"Device Mobile Speaker Icon\"\r\n      width=\"22\"\r\n      height=\"22\"\r\n    />\r\n\r\n  );\r\n};\r\nexport const ViewCartBaseBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-cart-00ADEF.svg\" alt=\"View Cart BaseBlue\" />\r\n  );\r\n};\r\nexport const ViewCartDarkBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-cart-04498C.svg\" alt=\"View Cart Dark Blue\" />\r\n\r\n  );\r\n};\r\nexport const ViewCartGray = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-cart-808080.svg\" alt=\"View Cart Gray\" />\r\n\r\n  );\r\n};\r\nexport const CheckoutCardBaseBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-card-00ADEF.svg\" alt=\"Checkout Card BaseBlue\" />\r\n  );\r\n};\r\nexport const CheckoutCardDarkBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-card-04498C.svg\" alt=\"Checkout Card BaseBlue\" />\r\n  );\r\n};\r\nexport const CheckoutCardGray = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-card-808080.svg\" alt=\"Checkout Card Gray\" />\r\n  );\r\n};\r\nexport const AccessBaseBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-access-00ADEF.svg\" alt=\"Access Base Blue\" />\r\n  );\r\n};\r\nexport const AccessDarkBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-access-04498C.svg\" alt=\"Access Dark Blue\" />\r\n  );\r\n};\r\nexport const AccessGray = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-access-808080.svg\" alt=\"Access Gray\" />\r\n  );\r\n};\r\nexport const TopRightArrowIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"13\"\r\n      height=\"13\"\r\n      viewBox=\"0 0 13 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M0.832189 10.79L9.12219 2.5H3.54219C3.27697 2.5 3.02262 2.39464 2.83508 2.20711C2.64754 2.01957 2.54219 1.76522 2.54219 1.5C2.54219 1.23478 2.64754 0.98043 2.83508 0.792893C3.02262 0.605357 3.27697 0.5 3.54219 0.5H11.4922C11.7574 0.5 12.0118 0.605357 12.1993 0.792893C12.3868 0.98043 12.4922 1.23478 12.4922 1.5V9.5C12.4922 9.76522 12.3868 10.0196 12.1993 10.2071C12.0118 10.3946 11.7574 10.5 11.4922 10.5H11.5422C11.277 10.5 11.0226 10.3946 10.8351 10.2071C10.6475 10.0196 10.5422 9.76522 10.5422 9.5V3.95L2.28219 12.21C2.18922 12.3037 2.07862 12.3781 1.95677 12.4289C1.83491 12.4797 1.7042 12.5058 1.57219 12.5058C1.44018 12.5058 1.30947 12.4797 1.18761 12.4289C1.06575 12.3781 0.955151 12.3037 0.862187 12.21C0.766468 12.119 0.689713 12.01 0.636353 11.8892C0.582994 11.7684 0.554085 11.6383 0.551295 11.5063C0.548506 11.3742 0.571892 11.243 0.620103 11.12C0.668314 10.9971 0.740396 10.8849 0.832189 10.79Z\"\r\n        fill=\"#00ADEF\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const MoneyWithWings = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-money-wings.svg\" alt=\"Money Wings Icon\" />\r\n  );\r\n};\r\nexport const FlatBlueBook = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-book-00A6ED.svg\" alt=\"Flat Blue Book\" />\r\n  );\r\n};\r\n\r\nexport const RedCircleCrossIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-failure-red.svg\" alt=\"Red Cross\" />\r\n  );\r\n};\r\n\r\nexport const WhiteCrossCircle = () => {\r\n  return (\r\n    <svg width=\"28\" height=\"28\" viewBox=\"0 0 28 28\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <path d=\"M14 0C6.2 0 0 6.2 0 14C0 21.8 6.2 28 14 28C21.8 28 28 21.8 28 14C28 6.2 21.8 0 14 0ZM19.4 21L14 15.6L8.6 21L7 19.4L12.4 14L7 8.6L8.6 7L14 12.4L19.4 7L21 8.6L15.6 14L21 19.4L19.4 21Z\" fill=\"white\" />\r\n    </svg>\r\n\r\n  )\r\n}\r\nexport const BlackCross = ({ width = 14, height = 14 }) => {\r\n  return (\r\n    <svg width={width} height={height} viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <path d=\"M1.4 14L0 12.6L5.6 7L0 1.4L1.4 0L7 5.6L12.6 0L14 1.4L8.4 7L14 12.6L12.6 14L7 8.4L1.4 14Z\" fill=\"black\" />\r\n    </svg>\r\n\r\n  )\r\n}\r\n\r\nexport const AddPlusIcon = ({ width = 10, height = 10 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-plus.svg\" width={width} height={height} alt=\"Add Model Icon\" />\r\n\r\n  );\r\n};\r\nexport const DragDropIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-drag-drop-icon.svg\" alt=\"Drap Drop Icon\" />\r\n\r\n  );\r\n};\r\nexport const SolidIncon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-info-marker.svg\" alt=\"Info Marker Icon\" />\r\n  );\r\n};\r\nexport const BlackDownIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-black-down-arrow.svg\" alt=\"Info Marker Icon\" />\r\n  );\r\n};\r\n\r\nexport const YellowInfoHexa = () => {\r\n  return (\r\n    <svg\r\n      width=\"12\"\r\n      height=\"14\"\r\n      viewBox=\"0 0 12 14\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path fillRule=\"evenodd\" clipRule=\"evenodd\" d=\"M3.22867 1.53727C4.58133 0.736604 5.25733 0.335938 6 0.335938C6.74267 0.335938 7.41867 0.735938 8.77133 1.53727L9.22867 1.80794C10.5813 2.60927 11.2573 3.00994 11.6287 3.66927C12 4.32927 12 5.12927 12 6.73194V7.27327C12 8.87527 12 9.6766 11.6287 10.3359C11.2573 10.9959 10.5813 11.3959 9.22867 12.1966L8.77133 12.4679C7.41867 13.2686 6.74267 13.6693 6 13.6693C5.25733 13.6693 4.58133 13.2693 3.22867 12.4679L2.77133 12.1966C1.41867 11.3966 0.742667 10.9953 0.371333 10.3359C-3.97364e-08 9.67594 0 8.87594 0 7.27327V6.73194C0 5.12927 -3.97364e-08 4.3286 0.371333 3.66927C0.742667 3.00927 1.41867 2.60927 2.77133 1.80794L3.22867 1.53727ZM6.66667 9.66927C6.66667 9.84608 6.59643 10.0157 6.4714 10.1407C6.34638 10.2657 6.17681 10.3359 6 10.3359C5.82319 10.3359 5.65362 10.2657 5.5286 10.1407C5.40357 10.0157 5.33333 9.84608 5.33333 9.66927C5.33333 9.49246 5.40357 9.32289 5.5286 9.19787C5.65362 9.07284 5.82319 9.0026 6 9.0026C6.17681 9.0026 6.34638 9.07284 6.4714 9.19787C6.59643 9.32289 6.66667 9.49246 6.66667 9.66927ZM6 3.16927C6.13261 3.16927 6.25979 3.22195 6.35355 3.31572C6.44732 3.40949 6.5 3.53666 6.5 3.66927V7.66927C6.5 7.80188 6.44732 7.92906 6.35355 8.02282C6.25979 8.11659 6.13261 8.16927 6 8.16927C5.86739 8.16927 5.74022 8.11659 5.64645 8.02282C5.55268 7.92906 5.5 7.80188 5.5 7.66927V3.66927C5.5 3.53666 5.55268 3.40949 5.64645 3.31572C5.74022 3.22195 5.86739 3.16927 6 3.16927Z\" fill=\"#FEA500\" />\r\n    </svg>\r\n\r\n  );\r\n};\r\n\r\nexport const TripleDotsMenu = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-kebab-menu.svg\" alt=\"Kabab Menu\" />\r\n  );\r\n};\r\n\r\nexport const WhiteDownArrow = () => {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"13\" height=\"6\" viewBox=\"0 0 14 8\" fill=\"none\">\r\n      <path d=\"M0.666504 0.667969L7.33317 7.33463L13.9998 0.667969H0.666504Z\" fill=\"white\" />\r\n    </svg>\r\n    // <img src=\"http://cdn.tradereply.com/dev/site-assets/icons/tradereply-white-down-arrow.svg\" width={width} height={height} alt=\"white down arrow\" />\r\n  );\r\n};\r\nexport const BlackDownArrow = () => {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"13\" height=\"6\" viewBox=\"0 0 14 8\" fill=\"none\">\r\n      <path d=\"M0.666504 0.667969L7.33317 7.33463L13.9998 0.667969H0.666504Z\" fill=\"black\" />\r\n    </svg>\r\n  );\r\n};\r\nexport const WhiteCrossIcon = () => {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"15\" viewBox=\"0 0 16 15\" fill=\"none\">\r\n      <path\r\n        d=\"M9.05969 7.49973L15.2847 1.2841C15.4043 1.13838 15.4654 0.953383 15.4562 0.765094C15.4469 0.576804 15.368 0.398686 15.2347 0.265385C15.1014 0.132084 14.9232 0.0531312 14.7349 0.0438836C14.5467 0.0346361 14.3617 0.095755 14.2159 0.215352L8.00031 6.44035L1.78469 0.215352C1.63897 0.095755 1.45397 0.0346361 1.26568 0.0438836C1.07739 0.0531312 0.899272 0.132084 0.76597 0.265385C0.632669 0.398686 0.553717 0.576804 0.544469 0.765094C0.535221 0.953383 0.59634 1.13838 0.715938 1.2841L6.94094 7.49973L0.715938 13.7154C0.575101 13.8575 0.496094 14.0496 0.496094 14.2497C0.496094 14.4499 0.575101 14.6419 0.715938 14.7841C0.859293 14.9227 1.0509 15.0002 1.25031 15.0002C1.44972 15.0002 1.64133 14.9227 1.78469 14.7841L8.00031 8.5591L14.2159 14.7841C14.3593 14.9227 14.5509 15.0002 14.7503 15.0002C14.9497 15.0002 15.1413 14.9227 15.2847 14.7841C15.4255 14.6419 15.5045 14.4499 15.5045 14.2497C15.5045 14.0496 15.4255 13.8575 15.2847 13.7154L9.05969 7.49973Z\"\r\n        fill=\"white\" />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const ProfileUserDarkIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-profile-user-dark.svg\" alt=\"Profile Dark Icon\" />\r\n  );\r\n};\r\nexport const BlueLocationIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-location.svg\" alt=\"Blue Location Icon\" />\r\n  );\r\n};\r\nexport const RatingStarIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-star-single.svg\" alt=\"Rating Star Icon\" />\r\n  );\r\n};\r\nexport const ThumbUpIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-thumb-up-green.svg\" alt=\"Thumb Up Icon\" />\r\n  );\r\n};\r\nexport const ThumbDownIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-thumb-down-red.svg\" alt=\"Thumb Down Icon\" />\r\n  );\r\n};\r\nexport const BlackShareIcon = ({ width = 18, height = 13 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-share-icon.svg\" width={width} height={height} alt=\"Black Share Icon\" />\r\n  );\r\n};\r\nexport const StaticListingImg = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/user-uploads/marketplace/listing-12345.png\" alt=\"Black Share Icon\" />\r\n  );\r\n};\r\n\r\nexport const WhiteDropDownArrow = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-drop-arrow.svg\" alt=\"White DropDown Icon\" />\r\n  );\r\n};\r\nexport const WhiteSingleStack = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-single-stack.svg\" alt=\"White Single Stack Icon\" />\r\n  );\r\n};\r\nexport const WhiteDoubleStack = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-double-stack.svg\" alt=\"White Double Stack Icon\" />\r\n  );\r\n};\r\nexport const WhiteTripleStack = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-triple-stack.svg\" alt=\"White Triple Stack Icon\" />\r\n  );\r\n};\r\nexport const OpenNewtabIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-open-new-tab.svg\" alt=\"New Tab Icon\" />\r\n  );\r\n};\r\nexport const RenameIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-edit-pencil.svg\" alt=\"Rename Icon\" />\r\n  );\r\n};\r\nexport const DeleteDarkIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-trashcan-dark.svg\" alt=\"Delete Dark Icon\" />\r\n  );\r\n};\r\nexport const EyeDarkInsightIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye-dark-insights.svg\" alt=\"Eye Dark Insight Icon\" />\r\n  );\r\n};\r\nexport const EyeDarkIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye-dark.svg\" alt=\"Eye Dark Icon\" />\r\n  );\r\n};\r\nexport const FollowersIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-followers-icon.svg\" alt=\"Followers Icon\" />\r\n  );\r\n};\r\nexport const ShareLightStrIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-share-white-fill.svg\" alt=\"Followers Icon\" />\r\n  );\r\n};\r\nexport const RelistIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-relist-icon.svg\" alt=\"Relist Icon\" />\r\n  );\r\n};\r\nexport const DigitaLAssetIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-digital-asset.svg\" alt=\"Digital Asset Icon\" />\r\n  );\r\n};\r\nexport const LicenseIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-license.svg\" alt=\"license Icon\" />\r\n  );\r\n};\r\nexport const LightEyeIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye-light.svg\" alt=\"Light Eye Icon\" />\r\n  );\r\n};\r\nexport const AddBlueIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-add.svg\" alt=\"Add Blue Icon\" />\r\n  );\r\n};\r\nexport const PinIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-pin.svg\" alt=\"Pin Icon\" />\r\n  );\r\n};\r\n\r\nexport const RightArrowIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"8\"\r\n      height=\"13\"\r\n      viewBox=\"0 0 8 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M7.15694 7.21102L1.49994 12.868L0.0859375 11.454L5.03594 6.50401L0.0859375 1.55401L1.49994 0.140015L7.15694 5.79701C7.34441 5.98454 7.44972 6.23885 7.44972 6.50401C7.44972 6.76918 7.34441 7.02349 7.15694 7.21102Z\"\r\n        fill=\"white\">\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const EditIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"18\"\r\n      viewBox=\"0 0 18 18\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M17.71 4.04C18.1 3.65 18.1 3 17.71 2.63L15.37 0.289999C15 -0.100001 14.35 -0.100001 13.96 0.289999L12.12 2.12L15.87 5.87M0 14.25V18H3.75L14.81 6.93L11.06 3.18L0 14.25Z\" fill=\"#00ADEF\"></path>\r\n    </svg>\r\n  );\r\n};\r\nexport const PlusIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"33\"\r\n      height=\"32\"\r\n      viewBox=\"0 0 33 32\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M32.5 20H20.5V32H12.5V20H0.5V12H12.5V0H20.5V12H32.5V20Z\" fill=\"white\">\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const RemoveIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"19\"\r\n      viewBox=\"0 0 18 19\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M9 0.75C4.125 0.75 0.25 4.625 0.25 9.5C0.25 14.375 4.125 18.25 9 18.25C13.875 18.25 17.75 14.375 17.75 9.5C17.75 4.625 13.875 0.75 9 0.75ZM12.375 13.875L9 10.5L5.625 13.875L4.625 12.875L8 9.5L4.625 6.125L5.625 5.125L9 8.5L12.375 5.125L13.375 6.125L10 9.5L13.375 12.875L12.375 13.875Z\"\r\n        fill=\"white\">\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const RightSolidArrowIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"12\"\r\n      height=\"13\"\r\n      viewBox=\"0 0 12 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M10.9903 5.63999L1.94025 0.769994C1.18025 0.359994 0.300251 1.06999 0.540251 1.89999L1.78025 6.23999C1.83025 6.41999 1.83025 6.59999 1.78025 6.77999L0.540251 11.12C0.300251 11.95 1.18025 12.66 1.94025 12.25L10.9903 7.37999C11.1446 7.29563 11.2735 7.17127 11.3632 7.01995C11.453 6.86862 11.5004 6.69593 11.5004 6.51999C11.5004 6.34406 11.453 6.17136 11.3632 6.02004C11.2735 5.86872 11.1446 5.74435 10.9903 5.65999V5.63999Z\"\r\n        fill=\"#00ADEF\"\r\n      >\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const LocationIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"20\"\r\n      height=\"20\"\r\n      viewBox=\"0 0 20 20\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M10.0004 11.1912C11.4363 11.1912 12.6004 10.0272 12.6004 8.59121C12.6004 7.15527 11.4363 5.99121 10.0004 5.99121C8.56445 5.99121 7.40039 7.15527 7.40039 8.59121C7.40039 10.0272 8.56445 11.1912 10.0004 11.1912Z\"\r\n        stroke=\"#292D32\"\r\n        stroke-width=\"1.5\"\r\n      />\r\n      <path\r\n        d=\"M3.01675 7.07533C4.65842 -0.141339 15.3501 -0.133006 16.9834 7.08366C17.9417 11.317 15.3084 14.9003 13.0001 17.117C11.3251 18.7337 8.67508 18.7337 6.99175 17.117C4.69175 14.9003 2.05842 11.3087 3.01675 7.07533Z\"\r\n        stroke=\"#292D32\"\r\n        strokeWidth=\"1.5\"\r\n      />\r\n    </svg>\r\n\r\n  );\r\n};\r\n\r\nexport const BulletPointIcon = ({ width = 8, height = 8 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-bullet-point.svg\" width={width} height={height} alt=\"Bullet Point Icon\" />\r\n  );\r\n};\r\nexport const CoinWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-coins-white.svg\" alt=\"White Coin Icon\" />\r\n  );\r\n};\r\nexport const ProductFormatWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-product-format-white.svg\" alt=\"Product Format White Icon\" />\r\n  );\r\n};\r\nexport const GuageWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-gauge-white.svg\" alt=\"Guage White Icon\" />\r\n  );\r\n};\r\nexport const TradingPlatformWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-trading-platform-white.svg\" alt=\"Trading Platform White Icon\" />\r\n  );\r\n};\r\nexport const ShuffleWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-shuffle-white.svg\" alt=\"Shuffle White Icon\" />\r\n  );\r\n};\r\nexport const ClockWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-clock-white.svg\" alt=\"Clock White Icon\" />\r\n  );\r\n};\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,MAAM,UAAU;IACrB,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;KAfa;AAgBN,MAAM,WAAW,kBACtB,6LAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;kBAER,cAAA,6LAAC;YACC,IAAG;YACH,aAAU;YACV,GAAE;YACF,WAAU;YACV,MAAK;;;;;;;;;;;MAZE;AAgBN,MAAM,YAAY;IACvB,qBACE,6LAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;kBAER,cAAA,6LAAC;YACC,IAAG;YACH,aAAU;YACV,WAAU;sBAEV,cAAA,6LAAC;gBACC,IAAG;gBACH,aAAU;gBACV,WAAU;;kCAEV,6LAAC;wBACC,IAAG;wBACH,aAAU;wBACV,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,WAAU;wBACV,MAAK;;;;;;kCAEP,6LAAC;wBACC,IAAG;wBACH,aAAU;wBACV,WAAU;;0CAEV,6LAAC;gCACC,IAAG;gCACH,aAAU;gCACV,GAAE;gCACF,WAAU;gCACV,MAAK;gCACL,UAAS;;;;;;0CAEX,6LAAC;gCACC,IAAG;gCACH,aAAU;gCACV,GAAE;gCACF,MAAK;gCACL,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvB;MApDa;AAqDN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;MAJa;AAKN,MAAM,kBAAkB;IAC7B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;MAJa;AAKN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;MAJa;AAKN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QAAI,KAAI;QAAkF,KAAI;;;;;;AAEnG;MAJa;AAKN,MAAM,oBAAoB;IAC/B,qBACE,6LAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;MAJa;AAKN,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACrD,qBACE,6LAAC;QAAI,KAAI;QAAyE,OAAO;QAAO,QAAQ;QAAQ,KAAI;;;;;;AAExH;MAJa;AAKN,MAAM,YAAY;IACvB,qBACE,6LAAC;QAAI,KAAI;QAAyE,KAAI;;;;;;AAE1F;MAJa;AAKN,MAAM,aAAa;IACxB,qBACE,6LAAC;QAAI,KAAI;QAAyE,KAAI;;;;;;AAE1F;OAJa;AAKN,MAAM,gBAAgB,CAAC,EAAE,KAAK,EAAE;IACrC,qBACE,6LAAC;QACC,KAAI;QACJ,KAAI;QACJ,WAAW;;;;;;AAGjB;OARa;AASN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;OAJa;AAKN,MAAM,UAAU;IACrB,qBACE,6LAAC;QAAI,KAAI;QAAsE,KAAI;;;;;;AAEvF;OAJa;AAKN,MAAM,aAAa;IACxB,qBACE,6LAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;OAJa;AAKN,MAAM,YAAY;IACvB,qBACE,6LAAC;QAAI,KAAI;QAAyE,KAAI;;;;;;AAE1F;OAJa;AAKN,MAAM,YAAY;IACvB,qBACE,6LAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;OAJa;AAKN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;OAJa;AAKN,MAAM,YAAY;IACvB,qBACE,6LAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;OAJa;AAKN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;OAJa;AAKN,MAAM,gBAAgB;IAC3B,OACE,OAAO;IACP,eAAe;IACf,gBAAgB;IAChB,wBAAwB;IACxB,gBAAgB;IAChB,uCAAuC;IACvC,IAAI;IACJ,UAAU;IACV,4jCAA4jC;IAC5jC,mBAAmB;IACnB,OAAO;IACP,SAAS;kBACT,6LAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;OAhBa;AAiBN,MAAM,WAAW;IACtB,qBACE,6LAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;OAJa;AAKN,MAAM,YAAY;IACvB,qBACE,6LAAC;QAAI,KAAI;QAAwE,KAAI;QACnF,OAAM;QACN,QAAO;;;;;;AAGb;OAPa;AAQN,MAAM,YAAY,CAAC,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,EAAE;IACnD,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;QACrF,OAAO;QACP,QAAQ;;;;;;AAGd;OAPa;AAQN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;OAJa;AAKN,MAAM,WAAW,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,EAAE;IACzD,qBACE,6LAAC;QAAI,KAAI;QAAuE,KAAI;QAClF,OAAO;QACP,QAAQ;QACR,WAAW;;;;;;AAGjB;OARa;AASN,MAAM,YAAY,CAAC,EAAE,QAAQ,MAAM,EAAE,SAAS,KAAK,EAAE;IAC1D,qBACE,6LAAC;QAAI,KAAI;QAAwE,QAAQ;QAAQ,OAAO;QAAO,KAAI;;;;;;AAEvH;OAJa;AAKN,MAAM,kBAAkB;IAC7B,qBACE,6LAAC;QAAI,KAAI;QAAmF,KAAI;;;;;;AAEpG;OAJa;AAKN,MAAM,qBAAqB;IAChC,qBACE,6LAAC;QAAI,KAAI;QAAmF,KAAI;QAC9F,OAAM;QACN,QAAO;;;;;;AAGb;OAPa;AAQN,MAAM,sBAAsB;IACjC,qBACE,6LAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;OAJa;AAKN,MAAM,cAAc;IACzB,qBACE,6LAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;kBACL,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAda;AAeN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;OAJa;AAKN,MAAM,sBAAsB;IACjC,qBACE,6LAAC;QAAI,KAAI;QAAoF,KAAI;;;;;;AAErG;OAJa;AAKN,MAAM,gBAAgB,CAAC,EAAE,QAAQ,MAAM,EAAE,SAAS,MAAM,EAAE;IAC/D,qBACE,6LAAC;QAAI,KAAI;QAA8E,QAAQ;QAAQ,OAAO;QAAO,KAAI;;;;;;AAE7H;OAJa;AAKN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;OAJa;AAKN,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE;IACtC,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;QACzF,WAAW;;;;;;AAGjB;OANa;AAON,MAAM,WAAW;IACtB,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAfa;AAgBN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;OAJa;AAKN,MAAM,WAAW;IACtB,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;OAJa;AAKN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAAsE,KAAI;;;;;;AAEvF;OAJa;AAKN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;OAJa;AAKN,MAAM,aAAa;IACxB,qBACE,6LAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;OAJa;AAKN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;OAJa;AAKN,MAAM,WAAW;IACtB,qBACE,6LAAC;QAAI,KAAI;QAAuE,KAAI;;;;;;AAExF;OAJa;AAKN,MAAM,WAAW;IACtB,qBACE,6LAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;OAJa;AAKN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;OAJa;AAKN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAfa;AAgBN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;OAJa;AAKN,MAAM,WAAW;IACtB,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;OAJa;AAMN,MAAM,YAAY,CAAC,EAAE,KAAK,EAAE;IACjC,OACE,OAAO;IACP,eAAe;IACf,gBAAgB;IAChB,wBAAwB;IACxB,gBAAgB;IAChB,uCAAuC;IACvC,IAAI;IACJ,UAAU;IACV,sSAAsS;IACtS,mBAAmB;IACnB,OAAO;IACP,SAAS;kBACT,6LAAC;QAAI,KAAI;QAA0E,KAAI;QACrF,OAAM;QACN,QAAO;QAEP,WAAW;;;;;;AAGjB;OArBa;AAsBN,MAAM,oBAAoB;IAC/B,qBACE,6LAAC;QAAI,KAAI;QAAqF,KAAI;;;;;;AAEtG;OAJa;AAKN,MAAM,sBAAsB;IACjC,qBACE,6LAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;OAJa;AAKN,MAAM,yBAAyB;IACpC,qBACE,6LAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;OAJa;AAKN,MAAM,sBAAsB;IACjC,qBACE,6LAAC;QAAI,KAAI;QAAmF,KAAI;;;;;;AAEpG;OAJa;AAKN,MAAM,uBAAuB;IAClC,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;OAJa;AAKN,MAAM,kBAAkB;IAC7B,qBACE,6LAAC;QAAI,KAAI;QAAuE,KAAI;;;;;;AAExF;OAJa;AAKN,MAAM,aAAa;IACxB,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAfa;AAgBN,MAAM,oBAAoB;IAC/B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;0BACC,cAAA,6LAAC;oBACC,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,eAAc;oBACd,mBAAkB;;sCAElB,6LAAC;4BAAK,WAAU;;;;;;sCAChB,6LAAC;4BAAK,QAAO;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKrC;OA5Ba;AA6BN,MAAM,iBAAiB;IAC5B,OACE,OAAO;IACP,eAAe;IACf,gBAAgB;IAChB,wBAAwB;IACxB,gBAAgB;IAChB,uCAAuC;IACvC,IAAI;IACJ,UAAU;IACV,o2BAAo2B;IACp2B,qBAAqB;IACrB,OAAO;IACP,SAAS;kBACT,6LAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;OAhBa;AAiBN,MAAM,oBAAoB;IAC/B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAfa;AAgBN,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACvD,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAfa;AAgBN,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACzD,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,OAAO;YAAE,WAAW;QAAiB;kBAErC,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAhBa;AAiBN,MAAM,oBAAoB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,CAAC,EAAE;IAC1D,qBACE,6LAAC;QAAI,OAAO;QACV,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;kBACN,cAAA,6LAAC;YAAK,GAAE;YAAgE,MAAK;;;;;;;;;;;AAInF;OAXa;AAYN,MAAM,yBAAyB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,CAAC,EAAE;IAC/D,qBACE,6LAAC;QAAI,OAAO;QACV,QAAQ;QACR,SAAQ;QAAW,MAAK;QAAO,OAAM;kBACrC,cAAA,6LAAC;YAAK,GAAE;YAAgE,MAAK;;;;;;;;;;;AAGnF;OARa;AAUN,MAAM,aAAa;IACxB,qBACE,6LAAC;QAAI,KAAI;QAAyE,KAAI;QACpF,OAAM;QACN,QAAO;;;;;;AAGb;OAPa;AAQN,MAAM,YAAY;IACvB,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAfa;AAgBN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,6LAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;;;;;;0BAEhB,6LAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;;;;;;0BAEhB,6LAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;;;;;;;;;;;;AAItB;OA7Ba;AA8BN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;QACrF,OAAM;QACN,QAAO;;;;;;AAGb;OAPa;AAQN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QAAI,KAAI;QAA+E,KAAI;QAC1F,OAAM;QACN,QAAO;;;;;;AAGb;OAPa;AAQN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;QACrF,OAAM;QACN,QAAO;;;;;;AAGb;OAPa;AAQN,MAAM,YAAY;IACvB,qBACE,6LAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAElG;OAJa;AAKN,MAAM,kBAAkB;IAC7B,qBACE,6LAAC;QACC,IAAG;QACH,aAAU;QACV,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;kBAER,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAhBa;AAiBN,MAAM,iBAAiB,CAAC,EAAE,UAAU,EAAE;IAC3C,qBACE,6LAAC;QACC,WAAW,aAAa,WAAW;QACnC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAhBa;AAiBN,MAAM,yBAAyB;IACpC,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAMb;OAjBa;AAkBN,MAAM,iBAAiB;IAC5B,OACE,OAAO;IACP,uCAAuC;IACvC,eAAe;IACf,gBAAgB;IAChB,wBAAwB;IACxB,mBAAmB;IACnB,IAAI;IACJ,UAAU;IACV,yHAAyH;IACzH,oBAAoB;IACpB,OAAO;IACP,SAAS;kBACT,6LAAC;QAAI,KAAI;QACP,KAAI;QACJ,OAAM;QACN,QAAO;;;;;;AAGb;OApBa;AAqBN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAKb;OA1Ba;AA2BN,MAAM,sBAAsB;IACjC,qBACE,6LAAC;QAAI,KAAI;QAAgF,KAAI;QAC3F,OAAM;QACN,QAAO;;;;;;AAIb;OARa;AASN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;OAJa;AAKN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAG/F;OALa;AAMN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAG/F;OALa;AAMN,MAAM,uBAAuB;IAClC,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;OAJa;AAKN,MAAM,uBAAuB;IAClC,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;OAJa;AAKN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;OAJa;AAKN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;OAJa;AAKN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;OAJa;AAKN,MAAM,aAAa;IACxB,qBACE,6LAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;OAJa;AAKN,MAAM,oBAAoB;IAC/B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAfa;AAgBN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;OAJa;AAKN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;OAJa;AAMN,MAAM,qBAAqB;IAChC,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;OAJa;AAMN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,OAAM;kBAChE,cAAA,6LAAC;YAAK,GAAE;YAAwL,MAAK;;;;;;;;;;;AAI3M;OAPa;AAQN,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACpD,qBACE,6LAAC;QAAI,OAAO;QAAO,QAAQ;QAAQ,SAAQ;QAAY,MAAK;QAAO,OAAM;kBACvE,cAAA,6LAAC;YAAK,GAAE;YAA2F,MAAK;;;;;;;;;;;AAI9G;OAPa;AASN,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACrD,qBACE,6LAAC;QAAI,KAAI;QAAuE,OAAO;QAAO,QAAQ;QAAQ,KAAI;;;;;;AAGtH;OALa;AAMN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAGlG;OALa;AAMN,MAAM,aAAa;IACxB,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;OAJa;AAKN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QAAI,KAAI;QAAmF,KAAI;;;;;;AAEpG;OAJa;AAMN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YAAK,UAAS;YAAU,UAAS;YAAU,GAAE;YAAm3C,MAAK;;;;;;;;;;;AAI56C;OAba;AAeN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;OAJa;AAMN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAI,SAAQ;QAAW,MAAK;kBACpF,cAAA,6LAAC;YAAK,GAAE;YAAgE,MAAK;;;;;;;;;;;AAInF;OAPa;AAQN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAI,SAAQ;QAAW,MAAK;kBACpF,cAAA,6LAAC;YAAK,GAAE;YAAgE,MAAK;;;;;;;;;;;AAGnF;OANa;AAON,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;kBACtF,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAGb;QARa;AAUN,MAAM,sBAAsB;IACjC,qBACE,6LAAC;QAAI,KAAI;QAAoF,KAAI;;;;;;AAErG;QAJa;AAKN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;QAJa;AAKN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;QAJa;AAKN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAElG;QAJa;AAKN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAElG;QAJa;AAKN,MAAM,iBAAiB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACxD,qBACE,6LAAC;QAAI,KAAI;QAA6E,OAAO;QAAO,QAAQ;QAAQ,KAAI;;;;;;AAE5H;QAJa;AAKN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;QAJa;AAMN,MAAM,qBAAqB;IAChC,qBACE,6LAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;QAJa;AAKN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;QAJa;AAKN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;QAJa;AAKN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;QAJa;AAKN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;QAJa;AAKN,MAAM,aAAa;IACxB,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;QAJa;AAKN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;QAJa;AAKN,MAAM,qBAAqB;IAChC,qBACE,6LAAC;QAAI,KAAI;QAAoF,KAAI;;;;;;AAErG;QAJa;AAKN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;QAJa;AAKN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAElG;QAJa;AAKN,MAAM,oBAAoB;IAC/B,qBACE,6LAAC;QAAI,KAAI;QAAmF,KAAI;;;;;;AAEpG;QAJa;AAKN,MAAM,aAAa;IACxB,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;QAJa;AAKN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;QAJa;AAKN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;QAJa;AAKN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;QAJa;AAKN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAAsE,KAAI;;;;;;AAEvF;QAJa;AAKN,MAAM,UAAU;IACrB,qBACE,6LAAC;QAAI,KAAI;QAAsE,KAAI;;;;;;AAEvF;QAJa;AAMN,MAAM,oBAAoB;IAC/B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YAAK,UAAS;YACb,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;QAhBa;AAiBN,MAAM,cAAc;IACzB,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YAA0K,MAAK;;;;;;;;;;;AAGzL;QAba;AAcN,MAAM,cAAc;IACzB,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YAA0D,MAAK;;;;;;;;;;;AAIzE;QAda;AAeN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;QAfa;AAgBN,MAAM,yBAAyB;IACpC,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAKb;QAhBa;AAiBN,MAAM,kBAAkB;IAC7B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,6LAAC;gBACC,GAAE;gBACF,QAAO;gBACP,gBAAa;;;;;;0BAEf,6LAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;;;;;;;AAKpB;QAtBa;AAwBN,MAAM,kBAAkB,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS,CAAC,EAAE;IACvD,qBACE,6LAAC;QAAI,KAAI;QAA+E,OAAO;QAAO,QAAQ;QAAQ,KAAI;;;;;;AAE9H;QAJa;AAKN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;QAJa;AAKN,MAAM,yBAAyB;IACpC,qBACE,6LAAC;QAAI,KAAI;QAAuF,KAAI;;;;;;AAExG;QAJa;AAKN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;QAJa;AAKN,MAAM,2BAA2B;IACtC,qBACE,6LAAC;QAAI,KAAI;QAAyF,KAAI;;;;;;AAE1G;QAJa;AAKN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;QAJa;AAKN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;QAJa", "debugId": null}}, {"offset": {"line": 2407, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2413, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/CommonButton.jsx"], "sourcesContent": ["import \"../../css/common/CommonButton.scss\";\r\n\r\n/**COMMON BUTTON WITH DYNAMIC PROPS */\r\n/** COMMON BUTTON WITH DYNAMIC PROPS */\r\nconst CommonButton = (props) => {\r\n\r\n  return (\r\n    <button\r\n      \r\n      onClick={props?.onClick}\r\n      type={props?.type}\r\n      className={`btn-style ${props.className} ${props.fluid ? \"w-100\" : \"\"} ${props.transparent ? \"transparent\" : \"\"} ${props.white20 ? \"white20\" : \"\"} ${props.whiteBtn ? \"white-btn\" : \"\"}`}\r\n      disabled={props?.disabled}\r\n    >\r\n      {props.onlyIcon && <span className=\"onlyIcon\">{props.onlyIcon}</span>}\r\n\r\n      <div className=\"d-flex flex-column align-items-center text-center\">\r\n        <span>{props.title}</span>\r\n        <span className=\"d-block\">{props.trial}</span>\r\n        <span className=\"d-block\">{props.subtitle}</span>\r\n        {props.innerText && (\r\n          <span style={{ fontSize: \"0.70em\", lineHeight: \"1\" }}>{props.innerText}</span>\r\n        )}\r\n      </div>\r\n\r\n      {props.btnIcon && (\r\n        <img\r\n          src={props.btnIcon}\r\n          alt={props?.title ? `${props.title} icon` : \"Button icon\"}\r\n          className=\"\"\r\n        />\r\n      )}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default CommonButton;"], "names": [], "mappings": ";;;;;;AAEA,oCAAoC,GACpC,qCAAqC,GACrC,MAAM,eAAe,CAAC;IAEpB,qBACE,6LAAC;QAEC,SAAS,OAAO;QAChB,MAAM,OAAO;QACb,WAAW,CAAC,UAAU,EAAE,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,KAAK,GAAG,UAAU,GAAG,CAAC,EAAE,MAAM,WAAW,GAAG,gBAAgB,GAAG,CAAC,EAAE,MAAM,OAAO,GAAG,YAAY,GAAG,CAAC,EAAE,MAAM,QAAQ,GAAG,cAAc,IAAI;QACxL,UAAU,OAAO;;YAEhB,MAAM,QAAQ,kBAAI,6LAAC;gBAAK,WAAU;0BAAY,MAAM,QAAQ;;;;;;0BAE7D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAM,MAAM,KAAK;;;;;;kCAClB,6LAAC;wBAAK,WAAU;kCAAW,MAAM,KAAK;;;;;;kCACtC,6LAAC;wBAAK,WAAU;kCAAW,MAAM,QAAQ;;;;;;oBACxC,MAAM,SAAS,kBACd,6LAAC;wBAAK,OAAO;4BAAE,UAAU;4BAAU,YAAY;wBAAI;kCAAI,MAAM,SAAS;;;;;;;;;;;;YAIzE,MAAM,OAAO,kBACZ,6LAAC;gBACC,KAAK,MAAM,OAAO;gBAClB,KAAK,OAAO,QAAQ,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,GAAG;gBAC5C,WAAU;;;;;;;;;;;;AAKpB;KA9BM;uCAgCS", "debugId": null}}, {"offset": {"line": 2500, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2506, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/NavLink.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { usePathname } from \"next/navigation\";\r\n\r\nexport default function NavLink({ className = \"\", children, href, ...props }) {\r\n  const pathname = usePathname();\r\n\r\n  const isActive = pathname === href;\r\n  return (\r\n    <Link\r\n      href={href}\r\n      {...props}\r\n      className={`${className} ${isActive ? \"active\" : \"\"}`}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS,QAAQ,EAAE,YAAY,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,OAAO;;IAC1E,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW,aAAa;IAC9B,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACL,GAAG,KAAK;QACT,WAAW,GAAG,UAAU,CAAC,EAAE,WAAW,WAAW,IAAI;kBAEpD;;;;;;AAGP;GAbwB;;QACL,qIAAA,CAAA,cAAW;;;KADN", "debugId": null}}, {"offset": {"line": 2543, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2549, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Header.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { Container, Navbar, Dropdown } from \"react-bootstrap\";\r\nimport { useLanguage } from \"@/context/LanguageContext\";\r\nimport {\r\n  DashboardIcon,\r\n  HelpIcon,\r\n  PartnershipIcon,\r\n  ReferIcon,\r\n  SettingIcon,\r\n  SignoutIcon,\r\n  UserBlackIcon,\r\n  GlobalIcons,\r\n  UserBluekIcon\r\n} from \"@/assets/svgIcons/SvgIcon\";\r\n\r\nimport CommonButton from \"./CommonButton\";\r\nimport NavLink from \"./NavLink\";\r\nimport \"../../css/common/Header.scss\";\r\nimport { isEmpty } from \"lodash\";\r\nimport Link from \"next/link\";\r\nimport Cookies from \"js-cookie\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { logout } from \"@/utils/auth\";\r\n\r\n\r\nconst Header = () => {\r\n  const [loginToken, setLoginToken] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const pathname = usePathname();\r\n  const [isFreeUser, setIsFreeUser] = useState(false);\r\n  const [isPricingPage, setIsPricingPage] = useState(false);\r\n  const isHomePage = pathname === \"/\";\r\n  // const props = usePage();\r\n  const user = {};\r\n  const { language, changeLanguage } = useLanguage();\r\n  // const user = usePage().props.auth.user;\r\n  const signIn = !isEmpty(loginToken);\r\n  const [isActive, setIsActive] = useState(false);\r\n  const [isProductOpen, setIsProductOpen] = useState(false);\r\n  const [isLangOpen, setIsLangOpen] = useState(false);\r\n  const [isOpenLanguage, setIsOpenLanguage] = useState(false);\r\n  const [isDesktop, setIsDesktop] = useState(false);\r\n\r\n\r\n  const ref = useRef();\r\n\r\n  const toggleClass = () => setIsActive((prev) => !prev);\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsDesktop(window.innerWidth >= 1200);\r\n    };\r\n\r\n    handleResize();\r\n\r\n    window.addEventListener('resize', handleResize);\r\n\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, []);\r\n\r\n  const toggleProductDropdown = () => {\r\n    setIsProductOpen((prev) => !prev);\r\n  };\r\n\r\n  const handleProductMouseEnter = () => {\r\n    if (isDesktop) {\r\n      setIsProductOpen(true);\r\n    }\r\n  };\r\n\r\n  const handleProductMouseLeave = () => {\r\n    if (isDesktop) {\r\n      setIsProductOpen(false);\r\n    }\r\n  };\r\n\r\n  const toggleMobileLangDropdown = () => {\r\n    setIsLangOpen((prev) => !prev);\r\n  };\r\n\r\n  const toggleMobileLangEnter = () => {\r\n    if (isDesktop) {\r\n      setIsLangOpen(true);\r\n    }\r\n  }\r\n  const toggleMobileLangLeave = () => {\r\n    if (isDesktop) {\r\n      setIsLangOpen(false);\r\n    }\r\n  }\r\n\r\n  const toggleLanguageDropdown = () => {\r\n    setIsOpenLanguage((prev) => !prev);\r\n  };\r\n\r\n  const handleLanguageMouseEnter = () => setIsOpenLanguage(true);\r\n  const handleLanguageMouseLeave = () => setIsOpenLanguage(false);\r\n\r\n  const handleNavClick = () => {\r\n    if (ref.current && document.body.clientWidth < 1220) {\r\n      ref.current.click();\r\n    }\r\n  };\r\n\r\n  const logoutUser = async () => {\r\n    // First clear all local auth data\r\n    Cookies.remove(\"authToken\");\r\n    sessionStorage.clear();\r\n    localStorage.clear();\r\n    \r\n    // Then call the API to logout on server\r\n    const success = await logout();\r\n    \r\n    // Always redirect to login page\r\n    router.push(\"/login\");\r\n  };\r\n\r\n\r\n\r\n  const renderUserDropdown = () => <UserDropdown signIn={loginToken} />;\r\n\r\n  const lang = [\r\n    {\r\n      lang: \"en\",\r\n      title: \"English\"\r\n    },\r\n    {\r\n      lang: \"fr\",\r\n      title: \"French\"\r\n    },\r\n    {\r\n      lang: \"es\",\r\n      title: \"Español\"\r\n    }\r\n  ];\r\n  const changeLang = (lang) => {\r\n    changeLanguage(lang);\r\n  };\r\n\r\n  useEffect(() => {\r\n    const tokens = Cookies.get(\"authToken\");\r\n    setLoginToken(tokens);\r\n\r\n\r\n    setIsPricingPage(pathname === \"/pricing\");\r\n\r\n\r\n    try {\r\n      const user = JSON.parse(localStorage.getItem(\"user\"));\r\n      if (user?.subscription_id === 1) {\r\n        setIsFreeUser(true);\r\n      }\r\n    } catch (e) {\r\n      console.warn(\"Invalid user in localStorage\");\r\n    }\r\n\r\n    setLoading(false);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (isActive) {\r\n      document.body.style.overflow = \"hidden\";\r\n    } else {\r\n      document.body.style.overflow = \"auto\";\r\n    }\r\n  \r\n    // Cleanup on unmount\r\n    return () => {\r\n      document.body.style.overflow = \"auto\";\r\n    };\r\n  }, [isActive]);\r\n  return (\r\n    <header className={`${isHomePage ? \"home-page\" : \"\"}`}>\r\n      <div className={`siteHeader ${isActive ? \"openmenu\" : \"\"}`}>\r\n        <Navbar expand=\"xl\">\r\n          <Container>\r\n            <div className=\"d-flex align-items-center \">\r\n              <Navbar.Toggle ref={ref} onClick={toggleClass} />\r\n              <NavLink href=\"/\" className=\"brandLogo\">\r\n                {/* <Logo /> */}\r\n                <img src=\"https://cdn.tradereply.com/dev/site-assets/tradereply-trading-insights-logo.svg\" alt=\"Brand Logo\" />\r\n              </NavLink>\r\n            </div>\r\n            <Navbar.Collapse className=\"justify-content-center\">\r\n              <div className=\"d-flex justify-content-center align-items-center openmenuSidebar\">\r\n                <NavLink\r\n                  onClick={handleNavClick}\r\n                  href=\"/\"\r\n                  className=\"brandLogo d-block d-xl-none\"\r\n                >\r\n                  {/* <Logo /> */}\r\n                  <img src=\"https://cdn.tradereply.com/dev/site-assets/tradereply-trading-insights-logo.svg\" alt=\"Brand Logo\" />\r\n                </NavLink>\r\n                <Navbar.Toggle ref={ref} onClick={toggleClass} />\r\n              </div>\r\n\r\n              <div className=\"navMenu d-xl-flex\">\r\n                <NavLink\r\n                  onClick={handleNavClick}\r\n                  href=\"/marketplace\"\r\n                  className=\"nav-link\"\r\n                >\r\n                  Marketplace\r\n                </NavLink>\r\n\r\n                {/* Product Dropdown */}\r\n                <div\r\n                  className={`nav-item common_dropdown dropdown ${isProductOpen ? \"show\" : \"\"}`}\r\n                  onMouseEnter={handleProductMouseEnter}\r\n                  onMouseLeave={handleProductMouseLeave}\r\n                  onClick={toggleProductDropdown}\r\n                >\r\n                  <NavLink\r\n                    className=\"nav-link dropdown-toggle\"\r\n                    href=\"#\"\r\n                    id=\"navbarDropdown\"\r\n                    role=\"button\"\r\n                    aria-haspopup=\"true\"\r\n                    aria-expanded={isProductOpen ? \"true\" : \"false\"}\r\n                  >\r\n                    Products\r\n                  </NavLink>\r\n\r\n                  <div\r\n                    className={`dropdown-menu ${isProductOpen ? \"show\" : \"\"}`}\r\n                    aria-labelledby=\"navbarDropdown\"\r\n                  >\r\n                    {!isEmpty(loginToken) && (\r\n                      <NavLink onClick={handleNavClick} href=\"/dashboard/trading-calculator\" className=\"nav-link\">\r\n                        Trading Calculator\r\n                      </NavLink>\r\n                    )}\r\n                    {isEmpty(loginToken) && (\r\n                      <NavLink onClick={handleNavClick} href=\"/trading-calculator\" className=\"nav-link\">\r\n                        Trading Calculators\r\n                      </NavLink>\r\n                    )}\r\n                    <NavLink onClick={handleNavClick} href=\"/features\" className=\"nav-link\">\r\n                      Features\r\n                    </NavLink>\r\n                    {loginToken ? (\r\n                      // <Link\r\n                      //   onClick={handleNavClick}\r\n                      //   href={\r\n                      //     isFreeUser ? \"/pricing?source=free_header_menu_pricing&feature=buy_trial\"\r\n                      //       : \"/pricing?source=member_header_menu_pricing&feature=buy_trial\"\r\n                      //   }\r\n                      //   className=\"nav-link\"\r\n\r\n                      // >\r\n                      //   Pricing\r\n                      // </Link>\r\n                      <a\r\n                        href=\"#\"\r\n                        onClick={(e) => {\r\n                          e.preventDefault();\r\n                          handleNavClick(); // keep this if you want the sidebar to close\r\n                          const user = JSON.parse(localStorage.getItem(\"user\"));\r\n                          const isFree = user?.subscription_id === 1;\r\n                          console.log(\"user?.subscription_iddddddddd\",user?.subscription_id);\r\n                          \r\n                          const targetUrl = isFree\r\n                            ? \"/pricing?source=free_header_menu_pricing&feature=buy_trial\"\r\n                            : \"/pricing?source=member_header_menu_pricing&feature=buy_trial\";\r\n                          window.location.href = targetUrl;\r\n                        }}\r\n                        className=\"nav-link\"\r\n                      >\r\n                        Pricing\r\n                      </a>\r\n\r\n                    ) : (\r\n                      <NavLink onClick={handleNavClick} href=\"/pricing\"\r\n                        className=\"nav-link\"\r\n                      >\r\n                        Pricing\r\n                      </NavLink>\r\n                    )}\r\n\r\n\r\n                  </div>\r\n                </div>\r\n\r\n                <NavLink\r\n                  onClick={handleNavClick}\r\n                  href=\"/education\"\r\n                  className=\"nav-link\"\r\n                >\r\n                  Education\r\n                </NavLink>\r\n                <NavLink\r\n                  onClick={handleNavClick}\r\n                  href=\"/blog\"\r\n                  className=\"nav-link\"\r\n                >\r\n                  Blog\r\n                </NavLink>\r\n\r\n                {/* Language Dropdown */}\r\n                <div\r\n                  className={`nav-item common_dropdown dropdown d-block d-xl-none ${isLangOpen ? \"show\" : \"\"}`}\r\n                  onMouseEnter={toggleMobileLangEnter}\r\n                  onMouseLeave={toggleMobileLangLeave}\r\n                  onClick={toggleMobileLangDropdown}\r\n                >\r\n                  <NavLink\r\n                    className=\"nav-link dropdown-toggle\"\r\n                    href=\"#\"\r\n                    id=\"navbarDropdown\"\r\n                    role=\"button\"\r\n                    aria-haspopup=\"true\"\r\n                    aria-expanded={isLangOpen ? \"true\" : \"false\"}\r\n                  >\r\n                    <span className=\"globalIcon\">\r\n                      <GlobalIcons />\r\n                    </span>\r\n                    <p className=\"text-capitalize fs-5 ms-2\">{language}</p>\r\n                  </NavLink>\r\n\r\n                  <div\r\n                    className={`dropdown-menu ${isLangOpen ? \"show\" : \"\"}`}\r\n                    aria-labelledby=\"navbarDropdown\"\r\n                  >\r\n                    {lang.map((Language) => (\r\n                      <NavLink\r\n                        onClick={() => changeLang(Language.lang)}\r\n                        key={Language.lang}\r\n                        href=\"\"\r\n                        className=\"nav-link text-white d-flex flex-column gap-3 fs-5 fw-bold\"\r\n                      >\r\n                        {Language.title}\r\n                      </NavLink>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n\r\n                <div className='d-block d-xl-none'>\r\n                  {/* <UserDropdown /> */}\r\n                  {signIn ? (\r\n                    <>\r\n                      <Link href=\"\" className='nav-link'><span className='me-3' onClick={() => logoutUser()}><SignoutIcon /></span> Sign Out</Link>\r\n                      {/* <NavLink href=\"/account-overview\" className='dropdown-item white_icon'><SettingIcon /> Account Settings</NavLink> */}\r\n                    </>\r\n                  ) : (\r\n                    <Link href=\"/login\" className='nav-link'><span className='me-3'><SignoutIcon /></span> Log In</Link>\r\n                  )}\r\n\r\n                  <Link href=\"/help\" className='nav-link'><span className='me-3'><HelpIcon /></span> Help Center</Link>\r\n                  <NavLink href=\"/partner\" className='nav-link white_stroke_icon'><span className='me-3'><PartnershipIcon /></span> Partnership</NavLink>\r\n                  <NavLink href=\"/refer-a-friend\" className='nav-link'><span className='me-3'><ReferIcon /></span> Refer A Friend</NavLink>\r\n                </div>\r\n              </div>\r\n            </Navbar.Collapse>\r\n\r\n            {isActive && (\r\n              <div\r\n                onClick={handleNavClick}\r\n                className=\"sidebar_backdrop d-xl-none\"\r\n              />\r\n            )}\r\n            {/* Language Desktop */}\r\n            <div className=\"languageDropdown d-none d-xl-flex\">\r\n              <div\r\n                className={`nav-item common_dropdown dropdown ${isOpenLanguage ? \"show\" : \"\"}`}\r\n                onMouseEnter={handleLanguageMouseEnter}\r\n                onMouseLeave={handleLanguageMouseLeave}\r\n                onClick={toggleLanguageDropdown}\r\n              >\r\n                <NavLink\r\n                  className=\"nav-link dropdown-toggle\"\r\n                  href=\"#\"\r\n                  id=\"navbarDropdown\"\r\n                  role=\"button\"\r\n                  aria-haspopup=\"true\"\r\n                  aria-expanded={isOpenLanguage ? \"true\" : \"false\"}\r\n                >\r\n                  <div className={`globalIcon ${isOpenLanguage ? \"active\" : \"\"}`}>\r\n                    <img\r\n                      src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-black-global.svg\"\r\n                      alt=\"Global Icon Black\"\r\n                      className=\"icon black\"\r\n                    />\r\n                    <img\r\n                      src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-brand-blue-global.svg\"\r\n                      alt=\"Global Icon Blue\"\r\n                      className=\"icon blue\"\r\n                    />\r\n                  </div>\r\n                  <p className=\"text-capitalize fs-5 ms-2\">{language}</p>\r\n                </NavLink>\r\n\r\n                <div\r\n                  className={`dropdown-menu ${isOpenLanguage ? \"show\" : \"\"}`}\r\n                  aria-labelledby=\"navbarDropdown\"\r\n                >\r\n                  {lang.map((Language) => (\r\n                    <NavLink\r\n                      onClick={() => changeLang(Language.lang)}\r\n                      key={Language.lang}\r\n                      href=\"\"\r\n                      className=\"nav-link text-white d-flex flex-column gap-3 p-2 px-3 fs-5 fw-bold\"\r\n                    >\r\n                      {Language.title}\r\n                    </NavLink>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"mx-2 mx-xl-4 d-none d-xl-block\">\r\n              {renderUserDropdown()}\r\n            </div>\r\n            {!loading && (\r\n              <>\r\n                {!loginToken && pathname !== \"/pricing\" && (\r\n                  <Link href=\"/pricing\">\r\n                    <CommonButton className=\"gradient-btn\" title=\"Get started\" />\r\n                  </Link>\r\n                )}\r\n\r\n                {loginToken && isFreeUser && !isPricingPage && (\r\n                  <Link href=\"/pricing?source=free_header_upgrade_button&feature=buy_trial\">\r\n                    <button className=\"btn-style gradient-btn \">\r\n                      <div className=\"d-flex flex-column align-items-center text-center gap-1\">\r\n                        <span style={{ lineHeight: \"1\" }} >Upgrade Now</span>\r\n                        <span style={{ fontSize: \"0.70em\", lineHeight: \"1\" }}>30-Day Free Trial</span>\r\n                      </div>\r\n                    </button>\r\n                  </Link>\r\n                )}\r\n              </>\r\n            )}\r\n\r\n          </Container>\r\n        </Navbar >\r\n      </div>\r\n    </header >\r\n  );\r\n};\r\n\r\nconst UserDropdown = ({ signIn }) => {\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n\r\n  const router = useRouter();\r\n\r\n  const logoutUser = async () => {\r\n    // First clear all local auth data\r\n    Cookies.remove(\"authToken\");\r\n    sessionStorage.clear();\r\n    localStorage.clear();\r\n    \r\n    // Then call the API to logout on server\r\n    const success = await logout();\r\n    \r\n    // Always redirect to login page\r\n    router.push(\"/login\");\r\n  };\r\n\r\n\r\n  return (\r\n    <Dropdown\r\n      align=\"end\"\r\n      className=\"common_dropdown userDropdown\"\r\n      show={isUserDropdownOpen}\r\n      onToggle={(isOpen) => setIsUserDropdownOpen(isOpen)}\r\n    >\r\n      <Dropdown.Toggle variant=\"\" id=\"dropdown-basic\">\r\n        <span className=\"user_icon\">\r\n          {isUserDropdownOpen ? <UserBluekIcon /> : <UserBlackIcon />}\r\n        </span>\r\n        <span className=\"user_name\"></span>\r\n      </Dropdown.Toggle>\r\n      <Dropdown.Menu>\r\n        {signIn ? (\r\n          <>\r\n            <NavLink\r\n              href=\"/dashboard\"\r\n              className=\"dropdown-item white_icon flex items-center\"\r\n            >\r\n              <DashboardIcon color=\"image_color_to_white\" /> Dashboard\r\n            </NavLink>\r\n            <NavLink\r\n              href=\"/help\"\r\n              className=\"dropdown-item white_icon flex items-center\"\r\n            >\r\n              <HelpIcon /> Help Center\r\n            </NavLink>\r\n            <NavLink\r\n              href=\"/account/overview\"\r\n              className=\"dropdown-item white_icon flex items-center\"\r\n            >\r\n              <SettingIcon color=\"image_color_to_white\" /> Account Settings\r\n            </NavLink>\r\n            <NavLink\r\n              href=\"/partner\"\r\n              className=\"dropdown-item white_stroke_icon flex items-center\"\r\n            >\r\n              <PartnershipIcon /> Partnership\r\n            </NavLink>\r\n            <NavLink\r\n              href=\"/refer-a-friend\"\r\n              className=\"dropdown-item white_icon flex items-center\"\r\n            >\r\n              <ReferIcon /> Refer A Friend\r\n            </NavLink>\r\n            <NavLink\r\n              href=\"/login\"\r\n              onClick={() => { logoutUser() }}\r\n              className=\"dropdown-item white_icon flex items-center\"\r\n            >\r\n              <SignoutIcon /> Log Out\r\n            </NavLink>\r\n          </>\r\n        ) : (\r\n          <>\r\n            <NavLink href=\"/login\" className=\"dropdown-item d-flex align-items-center\">\r\n              <SignoutIcon /> Log In\r\n            </NavLink>\r\n            <NavLink href=\"/help\" className=\"dropdown-item d-flex align-items-center\">\r\n              <HelpIcon /> Help Center\r\n            </NavLink>\r\n            <NavLink href=\"/partner\" className=\"dropdown-item d-flex align-items-center white_stroke_icon\">\r\n              <PartnershipIcon /> Partnership\r\n            </NavLink>\r\n            <NavLink href=\"/refer-a-friend\" className=\"dropdown-item d-flex align-items-center\">\r\n              <ReferIcon /> Refer A Friend\r\n            </NavLink>\r\n          </>\r\n        )}\r\n      </Dropdown.Menu>\r\n    </Dropdown>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAYA;AACA;AAEA;AACA;AACA;AAEA;;;AAzBA;;;;;;;;;;;;;;AA4BA,MAAM,SAAS;;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,aAAa,aAAa;IAChC,2BAA2B;IAC3B,MAAM,OAAO,CAAC;IACd,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,cAAW,AAAD;IAC/C,0CAA0C;IAC1C,MAAM,SAAS,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAO,AAAD,EAAE;IACxB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAG3C,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAEjB,MAAM,cAAc,IAAM,YAAY,CAAC,OAAS,CAAC;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,aAAa,OAAO,UAAU,IAAI;gBACpC;;YAEA;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAElC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,wBAAwB;QAC5B,iBAAiB,CAAC,OAAS,CAAC;IAC9B;IAEA,MAAM,0BAA0B;QAC9B,IAAI,WAAW;YACb,iBAAiB;QACnB;IACF;IAEA,MAAM,0BAA0B;QAC9B,IAAI,WAAW;YACb,iBAAiB;QACnB;IACF;IAEA,MAAM,2BAA2B;QAC/B,cAAc,CAAC,OAAS,CAAC;IAC3B;IAEA,MAAM,wBAAwB;QAC5B,IAAI,WAAW;YACb,cAAc;QAChB;IACF;IACA,MAAM,wBAAwB;QAC5B,IAAI,WAAW;YACb,cAAc;QAChB;IACF;IAEA,MAAM,yBAAyB;QAC7B,kBAAkB,CAAC,OAAS,CAAC;IAC/B;IAEA,MAAM,2BAA2B,IAAM,kBAAkB;IACzD,MAAM,2BAA2B,IAAM,kBAAkB;IAEzD,MAAM,iBAAiB;QACrB,IAAI,IAAI,OAAO,IAAI,SAAS,IAAI,CAAC,WAAW,GAAG,MAAM;YACnD,IAAI,OAAO,CAAC,KAAK;QACnB;IACF;IAEA,MAAM,aAAa;QACjB,kCAAkC;QAClC,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,eAAe,KAAK;QACpB,aAAa,KAAK;QAElB,wCAAwC;QACxC,MAAM,UAAU,MAAM,CAAA,GAAA,uHAAA,CAAA,SAAM,AAAD;QAE3B,gCAAgC;QAChC,OAAO,IAAI,CAAC;IACd;IAIA,MAAM,qBAAqB,kBAAM,6LAAC;YAAa,QAAQ;;;;;;IAEvD,MAAM,OAAO;QACX;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;QACT;KACD;IACD,MAAM,aAAa,CAAC;QAClB,eAAe;IACjB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM,SAAS,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YAC3B,cAAc;YAGd,iBAAiB,aAAa;YAG9B,IAAI;gBACF,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC;gBAC7C,IAAI,MAAM,oBAAoB,GAAG;oBAC/B,cAAc;gBAChB;YACF,EAAE,OAAO,GAAG;gBACV,QAAQ,IAAI,CAAC;YACf;YAEA,WAAW;QACb;2BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,UAAU;gBACZ,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA,qBAAqB;YACrB;oCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;2BAAG;QAAC;KAAS;IACb,qBACE,6LAAC;QAAO,WAAW,GAAG,aAAa,cAAc,IAAI;kBACnD,cAAA,6LAAC;YAAI,WAAW,CAAC,WAAW,EAAE,WAAW,aAAa,IAAI;sBACxD,cAAA,6LAAC,2LAAA,CAAA,SAAM;gBAAC,QAAO;0BACb,cAAA,6LAAC,iMAAA,CAAA,YAAS;;sCACR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2LAAA,CAAA,SAAM,CAAC,MAAM;oCAAC,KAAK;oCAAK,SAAS;;;;;;8CAClC,6LAAC,sIAAA,CAAA,UAAO;oCAAC,MAAK;oCAAI,WAAU;8CAE1B,cAAA,6LAAC;wCAAI,KAAI;wCAAkF,KAAI;;;;;;;;;;;;;;;;;sCAGnG,6LAAC,2LAAA,CAAA,SAAM,CAAC,QAAQ;4BAAC,WAAU;;8CACzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,sIAAA,CAAA,UAAO;4CACN,SAAS;4CACT,MAAK;4CACL,WAAU;sDAGV,cAAA,6LAAC;gDAAI,KAAI;gDAAkF,KAAI;;;;;;;;;;;sDAEjG,6LAAC,2LAAA,CAAA,SAAM,CAAC,MAAM;4CAAC,KAAK;4CAAK,SAAS;;;;;;;;;;;;8CAGpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,sIAAA,CAAA,UAAO;4CACN,SAAS;4CACT,MAAK;4CACL,WAAU;sDACX;;;;;;sDAKD,6LAAC;4CACC,WAAW,CAAC,kCAAkC,EAAE,gBAAgB,SAAS,IAAI;4CAC7E,cAAc;4CACd,cAAc;4CACd,SAAS;;8DAET,6LAAC,sIAAA,CAAA,UAAO;oDACN,WAAU;oDACV,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,iBAAc;oDACd,iBAAe,gBAAgB,SAAS;8DACzC;;;;;;8DAID,6LAAC;oDACC,WAAW,CAAC,cAAc,EAAE,gBAAgB,SAAS,IAAI;oDACzD,mBAAgB;;wDAEf,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAO,AAAD,EAAE,6BACR,6LAAC,sIAAA,CAAA,UAAO;4DAAC,SAAS;4DAAgB,MAAK;4DAAgC,WAAU;sEAAW;;;;;;wDAI7F,CAAA,GAAA,oIAAA,CAAA,UAAO,AAAD,EAAE,6BACP,6LAAC,sIAAA,CAAA,UAAO;4DAAC,SAAS;4DAAgB,MAAK;4DAAsB,WAAU;sEAAW;;;;;;sEAIpF,6LAAC,sIAAA,CAAA,UAAO;4DAAC,SAAS;4DAAgB,MAAK;4DAAY,WAAU;sEAAW;;;;;;wDAGvE,aACC,QAAQ;wDACR,6BAA6B;wDAC7B,WAAW;wDACX,gFAAgF;wDAChF,yEAAyE;wDACzE,MAAM;wDACN,yBAAyB;wDAEzB,IAAI;wDACJ,YAAY;wDACZ,UAAU;sEACV,6LAAC;4DACC,MAAK;4DACL,SAAS,CAAC;gEACR,EAAE,cAAc;gEAChB,kBAAkB,6CAA6C;gEAC/D,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC;gEAC7C,MAAM,SAAS,MAAM,oBAAoB;gEACzC,QAAQ,GAAG,CAAC,iCAAgC,MAAM;gEAElD,MAAM,YAAY,SACd,+DACA;gEACJ,OAAO,QAAQ,CAAC,IAAI,GAAG;4DACzB;4DACA,WAAU;sEACX;;;;;iFAKD,6LAAC,sIAAA,CAAA,UAAO;4DAAC,SAAS;4DAAgB,MAAK;4DACrC,WAAU;sEACX;;;;;;;;;;;;;;;;;;sDASP,6LAAC,sIAAA,CAAA,UAAO;4CACN,SAAS;4CACT,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,sIAAA,CAAA,UAAO;4CACN,SAAS;4CACT,MAAK;4CACL,WAAU;sDACX;;;;;;sDAKD,6LAAC;4CACC,WAAW,CAAC,oDAAoD,EAAE,aAAa,SAAS,IAAI;4CAC5F,cAAc;4CACd,cAAc;4CACd,SAAS;;8DAET,6LAAC,sIAAA,CAAA,UAAO;oDACN,WAAU;oDACV,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,iBAAc;oDACd,iBAAe,aAAa,SAAS;;sEAErC,6LAAC;4DAAK,WAAU;sEACd,cAAA,6LAAC,uIAAA,CAAA,cAAW;;;;;;;;;;sEAEd,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;8DAG5C,6LAAC;oDACC,WAAW,CAAC,cAAc,EAAE,aAAa,SAAS,IAAI;oDACtD,mBAAgB;8DAEf,KAAK,GAAG,CAAC,CAAC,yBACT,6LAAC,sIAAA,CAAA,UAAO;4DACN,SAAS,IAAM,WAAW,SAAS,IAAI;4DAEvC,MAAK;4DACL,WAAU;sEAET,SAAS,KAAK;2DAJV,SAAS,IAAI;;;;;;;;;;;;;;;;sDAU1B,6LAAC;4CAAI,WAAU;;gDAEZ,uBACC;8DACE,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAG,WAAU;;0EAAW,6LAAC;gEAAK,WAAU;gEAAO,SAAS,IAAM;0EAAc,cAAA,6LAAC,uIAAA,CAAA,cAAW;;;;;;;;;;4DAAU;;;;;;;kFAI/G,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAAW,6LAAC;4DAAK,WAAU;sEAAO,cAAA,6LAAC,uIAAA,CAAA,cAAW;;;;;;;;;;wDAAU;;;;;;;8DAGxF,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;;sEAAW,6LAAC;4DAAK,WAAU;sEAAO,cAAA,6LAAC,uIAAA,CAAA,WAAQ;;;;;;;;;;wDAAU;;;;;;;8DAClF,6LAAC,sIAAA,CAAA,UAAO;oDAAC,MAAK;oDAAW,WAAU;;sEAA6B,6LAAC;4DAAK,WAAU;sEAAO,cAAA,6LAAC,uIAAA,CAAA,kBAAe;;;;;;;;;;wDAAU;;;;;;;8DACjH,6LAAC,sIAAA,CAAA,UAAO;oDAAC,MAAK;oDAAkB,WAAU;;sEAAW,6LAAC;4DAAK,WAAU;sEAAO,cAAA,6LAAC,uIAAA,CAAA,YAAS;;;;;;;;;;wDAAU;;;;;;;;;;;;;;;;;;;;;;;;;wBAKrG,0BACC,6LAAC;4BACC,SAAS;4BACT,WAAU;;;;;;sCAId,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAW,CAAC,kCAAkC,EAAE,iBAAiB,SAAS,IAAI;gCAC9E,cAAc;gCACd,cAAc;gCACd,SAAS;;kDAET,6LAAC,sIAAA,CAAA,UAAO;wCACN,WAAU;wCACV,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,iBAAc;wCACd,iBAAe,iBAAiB,SAAS;;0DAEzC,6LAAC;gDAAI,WAAW,CAAC,WAAW,EAAE,iBAAiB,WAAW,IAAI;;kEAC5D,6LAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;kEAEZ,6LAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;;0DAGd,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAG5C,6LAAC;wCACC,WAAW,CAAC,cAAc,EAAE,iBAAiB,SAAS,IAAI;wCAC1D,mBAAgB;kDAEf,KAAK,GAAG,CAAC,CAAC,yBACT,6LAAC,sIAAA,CAAA,UAAO;gDACN,SAAS,IAAM,WAAW,SAAS,IAAI;gDAEvC,MAAK;gDACL,WAAU;0DAET,SAAS,KAAK;+CAJV,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;sCAU5B,6LAAC;4BAAI,WAAU;sCACZ;;;;;;wBAEF,CAAC,yBACA;;gCACG,CAAC,cAAc,aAAa,4BAC3B,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,2IAAA,CAAA,UAAY;wCAAC,WAAU;wCAAe,OAAM;;;;;;;;;;;gCAIhD,cAAc,cAAc,CAAC,+BAC5B,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC;wCAAO,WAAU;kDAChB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,OAAO;wDAAE,YAAY;oDAAI;8DAAI;;;;;;8DACnC,6LAAC;oDAAK,OAAO;wDAAE,UAAU;wDAAU,YAAY;oDAAI;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa9E;GA5ZM;;QAGa,qIAAA,CAAA,cAAW;QAMS,oIAAA,CAAA,cAAW;;;KAT5C;AA8ZN,MAAM,eAAe,CAAC,EAAE,MAAM,EAAE;;IAC9B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,UAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa;QACjB,kCAAkC;QAClC,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,eAAe,KAAK;QACpB,aAAa,KAAK;QAElB,wCAAwC;QACxC,MAAM,UAAU,MAAM,CAAA,GAAA,uHAAA,CAAA,SAAM,AAAD;QAE3B,gCAAgC;QAChC,QAAO,IAAI,CAAC;IACd;IAGA,qBACE,6LAAC,+LAAA,CAAA,WAAQ;QACP,OAAM;QACN,WAAU;QACV,MAAM;QACN,UAAU,CAAC,SAAW,sBAAsB;;0BAE5C,6LAAC,+LAAA,CAAA,WAAQ,CAAC,MAAM;gBAAC,SAAQ;gBAAG,IAAG;;kCAC7B,6LAAC;wBAAK,WAAU;kCACb,mCAAqB,6LAAC,uIAAA,CAAA,gBAAa;;;;iDAAM,6LAAC,uIAAA,CAAA,gBAAa;;;;;;;;;;kCAE1D,6LAAC;wBAAK,WAAU;;;;;;;;;;;;0BAElB,6LAAC,+LAAA,CAAA,WAAQ,CAAC,IAAI;0BACX,uBACC;;sCACE,6LAAC,sIAAA,CAAA,UAAO;4BACN,MAAK;4BACL,WAAU;;8CAEV,6LAAC,uIAAA,CAAA,gBAAa;oCAAC,OAAM;;;;;;gCAAyB;;;;;;;sCAEhD,6LAAC,sIAAA,CAAA,UAAO;4BACN,MAAK;4BACL,WAAU;;8CAEV,6LAAC,uIAAA,CAAA,WAAQ;;;;;gCAAG;;;;;;;sCAEd,6LAAC,sIAAA,CAAA,UAAO;4BACN,MAAK;4BACL,WAAU;;8CAEV,6LAAC,uIAAA,CAAA,cAAW;oCAAC,OAAM;;;;;;gCAAyB;;;;;;;sCAE9C,6LAAC,sIAAA,CAAA,UAAO;4BACN,MAAK;4BACL,WAAU;;8CAEV,6LAAC,uIAAA,CAAA,kBAAe;;;;;gCAAG;;;;;;;sCAErB,6LAAC,sIAAA,CAAA,UAAO;4BACN,MAAK;4BACL,WAAU;;8CAEV,6LAAC,uIAAA,CAAA,YAAS;;;;;gCAAG;;;;;;;sCAEf,6LAAC,sIAAA,CAAA,UAAO;4BACN,MAAK;4BACL,SAAS;gCAAQ;4BAAa;4BAC9B,WAAU;;8CAEV,6LAAC,uIAAA,CAAA,cAAW;;;;;gCAAG;;;;;;;;iDAInB;;sCACE,6LAAC,sIAAA,CAAA,UAAO;4BAAC,MAAK;4BAAS,WAAU;;8CAC/B,6LAAC,uIAAA,CAAA,cAAW;;;;;gCAAG;;;;;;;sCAEjB,6LAAC,sIAAA,CAAA,UAAO;4BAAC,MAAK;4BAAQ,WAAU;;8CAC9B,6LAAC,uIAAA,CAAA,WAAQ;;;;;gCAAG;;;;;;;sCAEd,6LAAC,sIAAA,CAAA,UAAO;4BAAC,MAAK;4BAAW,WAAU;;8CACjC,6LAAC,uIAAA,CAAA,kBAAe;;;;;gCAAG;;;;;;;sCAErB,6LAAC,sIAAA,CAAA,UAAO;4BAAC,MAAK;4BAAkB,WAAU;;8CACxC,6LAAC,uIAAA,CAAA,YAAS;;;;;gCAAG;;;;;;;;;;;;;;;;;;;;AAO3B;IA5FM;;QAGW,qIAAA,CAAA,YAAS;;;MAHpB;uCA8FS", "debugId": null}}, {"offset": {"line": 3581, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3587, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Footer.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Col, Container, Row } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport Cookies from \"js-cookie\";\r\n// import { Logo } from \"@/assets/svgIcons/SvgIcon\";\r\nimport \"../../css/common/Footer.scss\";\r\nimport { Image } from 'next/image';\r\nimport { useEffect, useState } from \"react\";\r\n\r\nconst Footer = () => {\r\n  const url = usePathname();\r\n  const [loginToken, setLoginToken] = useState(null);\r\n  const [isFreeUser, setIsFreeUser] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const tokens = Cookies.get(\"authToken\");\r\n    setLoginToken(tokens);\r\n\r\n\r\n    try {\r\n      const user = JSON.parse(localStorage.getItem(\"user\"));\r\n      if (user?.subscription_id === 1) {\r\n        setIsFreeUser(true);\r\n      }\r\n    } catch (e) {\r\n      console.warn(\"Invalid user in localStorage\");\r\n    }\r\n\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      <div className=\"site_footer\">\r\n        <div className=\"site_footer_inner\">\r\n          <Container>\r\n            <Row className=\"gx-xl-5\">\r\n              <Col md={4} sm={12} xs={12}>\r\n                <div className=\"site_footer_content\">\r\n                  <div className=\"site_footer_logo\">\r\n                    <Link prefetch={true} href=\"/\">\r\n                      <img src=\"https://cdn.tradereply.com/dev/site-assets/tradereply-trading-insights-logo.svg\" alt=\"Brand Logo\" />\r\n                    </Link>\r\n                  </div>\r\n                  <p>\r\n                    TradeReply is an advanced analytics suite designed for\r\n                    crypto and stock traders to input historical trading data\r\n                    and leverage powerful visuals, graphs, and metrics to\r\n                    optimize and develop effective trade strategies with\r\n                    real-time insights.\r\n                  </p>\r\n                </div>\r\n              </Col>\r\n              <Col md={8} sm={12} xs={12}>\r\n                <Row>\r\n                  <Col md={4} sm={4} xs={6}>\r\n                    <div className=\"site_footer_links\">\r\n                      <h4>Company</h4>\r\n                      <ul>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/help/hc/en-us/requests/new\" className={url == '/help/hc/en-us/requests/new' ? 'new-link' : ''}>Contact</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/brand-assets\" className={url == '/brand-assets' ? 'new-link' : ''}>Brand Assets</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/accessibility\" className={url == '/accessibility' ? 'new-link' : ''}>Accessibility</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/privacy\" className={url == '/privacy' ? 'new-link' : ''}>\r\n                            Privacy Policy\r\n                          </Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/cookies\" className={url == '/cookies' ? 'new-link' : ''}>\r\n                            Cookies Policy\r\n                          </Link>\r\n                        </li>\r\n                        <li>\r\n                          <a\r\n                            href=\"#\"\r\n                            onClick={(e) => {\r\n                              e.preventDefault(); // prevents scroll to top\r\n                              if (typeof Osano !== \"undefined\" && Osano.cm) {\r\n                                Osano.cm.showDrawer(\"osano-cm-dom-info-dialog-open\");\r\n                              }\r\n                            }}\r\n                          >\r\n                            Cookie Settings\r\n                          </a>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/terms\" className={url == '/terms' ? 'new-link' : ''}>\r\n                            Terms & Conditions\r\n                          </Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/disclaimer\" className={url == '/disclaimer' ? 'new-link' : ''}>\r\n                            Disclaimer\r\n                          </Link>\r\n                        </li>\r\n                      </ul>\r\n                    </div>\r\n                  </Col>\r\n\r\n                  <Col md={4} sm={4} xs={6}>\r\n                    <div className=\"site_footer_links\">\r\n                      <h4>Partners</h4>\r\n                      <ul>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/refer-a-friend\" className={url == '/refer-a-friend' ? 'new-link' : ''}>Refer a Friend</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/partner\" className={url == '/partner' ? 'new-link' : ''}>Partner Program</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/advertising\" className={url == '/advertising' ? 'new-link' : ''}>Advertising</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/features\" className={url == '/features' ? 'new-link' : ''}>Features</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/education\" className={url == '/education' ? 'new-link' : ''}>Education</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/brokers\" className={url == '/brokers' ? 'new-link' : ''}>Brokers</Link>\r\n                        </li>\r\n                      </ul>\r\n                    </div>\r\n                  </Col>\r\n                  <Col md={4} sm={4} xs={6}>\r\n                    <div className=\"site_footer_links\">\r\n                      <h4>Community</h4>\r\n                      <ul>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/help\" className={url == '/helpcenter' ? 'new-link' : ''}>Help Center</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/sitemap\" className={url == '/sitemap' ? 'new-link' : ''}>Sitemap</Link>\r\n                        </li>\r\n                        <li>\r\n                          {/* <Link prefetch={true}\r\n                            href={\r\n                              typeof window !== \"undefined\" && sessionStorage.getItem(\"plan\") === \"Free\"\r\n                                ? \"/pricing?source=free_footer_menu_pricing&feature=buy_trial\"\r\n                                : \"/pricing?source=member_footer_menu_pricing&feature=buy_trial\"\r\n                            } className={url == '/pricing' ? 'new-link' : ''}>Pricing</Link> */}\r\n\r\n                          {loginToken ? (\r\n                           <Link\r\n                             prefetch={true}\r\n                             href={\r\n                               loginToken\r\n                                 ? isFreeUser\r\n                                   ? \"/pricing?source=free_footer_menu_pricing&feature=buy_trial\"\r\n                                   : \"/pricing?source=member_footer_menu_pricing&feature=buy_trial\"\r\n                                 : \"/pricing\"\r\n                             }\r\n                             className={url == \"/pricing\" ? \"new-link\" : \"\"}\r\n                           >\r\n                             Pricing\r\n                           </Link>\r\n\r\n                          ) : (\r\n                            <Link prefetch={true}\r\n                              href=\"/pricing\"\r\n                              className={url == '/pricing' ? 'new-link' : ''}>Pricing</Link>\r\n                          )}\r\n\r\n\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/blog\" className={url == '/blog' ? 'new-link' : ''}>Blog</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/status\" className={url == '/status' ? 'new-link' : ''}>Status</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/help//hc/en-us/requests/new?ticket_form_id=37293785519643\" className={url == '/help//hc/en-us/requests/new?ticket_form_id=37293785519643' ? 'new-link' : ''}>Feedback/Bugs</Link>\r\n                        </li>\r\n                      </ul>\r\n                    </div>\r\n                  </Col>\r\n                </Row>\r\n              </Col>\r\n            </Row>\r\n          </Container>\r\n        </div>\r\n        <div className=\"site_footer_copyright\">\r\n          <Container>\r\n            <p>Copyright © 2025 TradeReply. All Rights Reserved.</p>\r\n          </Container>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AAGA;AACA;;;AATA;;;;;;;;AAWA,MAAM,SAAS;;IACb,MAAM,MAAM,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM,SAAS,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YAC3B,cAAc;YAGd,IAAI;gBACF,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC;gBAC7C,IAAI,MAAM,oBAAoB,GAAG;oBAC/B,cAAc;gBAChB;YACF,EAAE,OAAO,GAAG;gBACV,QAAQ,IAAI,CAAC;YACf;QAEF;2BAAG,EAAE;IAEL,qBACE;kBACE,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,iMAAA,CAAA,YAAS;kCACR,cAAA,6LAAC,qLAAA,CAAA,MAAG;4BAAC,WAAU;;8CACb,6LAAC,qLAAA,CAAA,MAAG;oCAAC,IAAI;oCAAG,IAAI;oCAAI,IAAI;8CACtB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,UAAU;oDAAM,MAAK;8DACzB,cAAA,6LAAC;wDAAI,KAAI;wDAAkF,KAAI;;;;;;;;;;;;;;;;0DAGnG,6LAAC;0DAAE;;;;;;;;;;;;;;;;;8CASP,6LAAC,qLAAA,CAAA,MAAG;oCAAC,IAAI;oCAAG,IAAI;oCAAI,IAAI;8CACtB,cAAA,6LAAC,qLAAA,CAAA,MAAG;;0DACF,6LAAC,qLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAG,IAAI;gDAAG,IAAI;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;;8EACC,6LAAC;8EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAA8B,WAAW,OAAO,gCAAgC,aAAa;kFAAI;;;;;;;;;;;8EAE9H,6LAAC;8EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAgB,WAAW,OAAO,kBAAkB,aAAa;kFAAI;;;;;;;;;;;8EAElG,6LAAC;8EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAiB,WAAW,OAAO,mBAAmB,aAAa;kFAAI;;;;;;;;;;;8EAEpG,6LAAC;8EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAW,WAAW,OAAO,aAAa,aAAa;kFAAI;;;;;;;;;;;8EAIxF,6LAAC;8EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAW,WAAW,OAAO,aAAa,aAAa;kFAAI;;;;;;;;;;;8EAIxF,6LAAC;8EACC,cAAA,6LAAC;wEACC,MAAK;wEACL,SAAS,CAAC;4EACR,EAAE,cAAc,IAAI,yBAAyB;4EAC7C,IAAI,OAAO,UAAU,eAAe,MAAM,EAAE,EAAE;gFAC5C,MAAM,EAAE,CAAC,UAAU,CAAC;4EACtB;wEACF;kFACD;;;;;;;;;;;8EAIH,6LAAC;8EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAS,WAAW,OAAO,WAAW,aAAa;kFAAI;;;;;;;;;;;8EAIpF,6LAAC;8EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAc,WAAW,OAAO,gBAAgB,aAAa;kFAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAQpG,6LAAC,qLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAG,IAAI;gDAAG,IAAI;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;;8EACC,6LAAC;8EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAkB,WAAW,OAAO,oBAAoB,aAAa;kFAAI;;;;;;;;;;;8EAEtG,6LAAC;8EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAW,WAAW,OAAO,aAAa,aAAa;kFAAI;;;;;;;;;;;8EAExF,6LAAC;8EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAe,WAAW,OAAO,iBAAiB,aAAa;kFAAI;;;;;;;;;;;8EAEhG,6LAAC;8EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAY,WAAW,OAAO,cAAc,aAAa;kFAAI;;;;;;;;;;;8EAE1F,6LAAC;8EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAa,WAAW,OAAO,eAAe,aAAa;kFAAI;;;;;;;;;;;8EAE5F,6LAAC;8EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAW,WAAW,OAAO,aAAa,aAAa;kFAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAK9F,6LAAC,qLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAG,IAAI;gDAAG,IAAI;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;;8EACC,6LAAC;8EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAQ,WAAW,OAAO,gBAAgB,aAAa;kFAAI;;;;;;;;;;;8EAExF,6LAAC;8EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAW,WAAW,OAAO,aAAa,aAAa;kFAAI;;;;;;;;;;;8EAExF,6LAAC;8EAQE,2BACA,6LAAC,+JAAA,CAAA,UAAI;wEACH,UAAU;wEACV,MACE,aACI,aACE,+DACA,iEACF;wEAEN,WAAW,OAAO,aAAa,aAAa;kFAC7C;;;;;6FAKA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,UAAU;wEACd,MAAK;wEACL,WAAW,OAAO,aAAa,aAAa;kFAAI;;;;;;;;;;;8EAKtD,6LAAC;8EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAQ,WAAW,OAAO,UAAU,aAAa;kFAAI;;;;;;;;;;;8EAElF,6LAAC;8EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAU,WAAW,OAAO,YAAY,aAAa;kFAAI;;;;;;;;;;;8EAEtF,6LAAC;8EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAA6D,WAAW,OAAO,+DAA+D,aAAa;kFAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAU5M,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,iMAAA,CAAA,YAAS;kCACR,cAAA,6LAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAMf;GA1LM;;QACQ,qIAAA,CAAA,cAAW;;;KADnB;uCA4LS", "debugId": null}}, {"offset": {"line": 4191, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4197, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/common/Account/AccountSidebar.js"], "sourcesContent": ["import {\r\n  CartSideIcon,\r\n  DollerIcon,\r\n  LinkIcon,\r\n  LockIcon,\r\n  PaymentIcon,\r\n  RightArrowIcon,\r\n  SecurityIcon,\r\n  BaseEyeIcon,\r\n  UserBlueIcon,\r\n  MarketplaceListIcon,\r\n  MarketplaceDisputeIcon,\r\n  SoldProductIcon,\r\n  PurchasedProductIcon,\r\n  SellerDashboardIcon,\r\n  PublicProfileIcon,\r\n} from \"@/assets/svgIcons/SvgIcon\";\r\nimport NavLink from \"@/Components/UI/NavLink\";\r\nimport \"@/css/account/AccountSidebar.scss\";\r\n\r\nconst AccountSidebarOptions = [\r\n  {\r\n    href: \"/account/overview\",\r\n    label: \"Account Overview\",\r\n    icon: <BaseEyeIcon />\r\n  },\r\n  {\r\n    href: \"/account/details\",\r\n    label: \"Account Details\",\r\n    icon: <UserBlueIcon />\r\n  },\r\n  {\r\n    href: \"/account/subscriptions\",\r\n    label: \"Subscriptions\",\r\n    icon: <DollerIcon />,\r\n  },\r\n  {\r\n    href: \"/account/security\",\r\n    label: \"Security\",\r\n    icon: <SecurityIcon />\r\n  },\r\n  {\r\n    href: \"/account/privacy\",\r\n    label: \"Privacy & Communication\",\r\n    icon: <LockIcon />,\r\n  },\r\n  {\r\n    href: \"/account/connections\",\r\n    label: \"Connections\",\r\n    icon: <LinkIcon />\r\n  },\r\n  {\r\n    href: \"/account/payments\",\r\n    label: \"Payment Methods\",\r\n    icon: <PaymentIcon />,\r\n  },\r\n  {\r\n    href: \"/account/transactions\",\r\n    label: \"Transaction History\",\r\n    icon: <CartSideIcon />,\r\n  },\r\n];\r\nconst MarketPlaceSidebarOpt = [\r\n  {\r\n    href: \"/account/public-profile\",\r\n    label: \"Public Profile\",\r\n    icon: <PublicProfileIcon />\r\n  },\r\n  {\r\n    href: \"/account/seller-dashboard\",\r\n    label: \"Seller Dashboard\",\r\n    icon: <SellerDashboardIcon />\r\n  },\r\n  {\r\n    href: \"/account/your-listings\",\r\n    label: \"Marketplace Listings\",\r\n    icon: <MarketplaceListIcon />,\r\n  },\r\n  {\r\n    href: \"/account/your-disputes\",\r\n    label: \"Marketplace Disputes\",\r\n    icon: <MarketplaceDisputeIcon />,\r\n  },\r\n  {\r\n    href: \"/account/sold-products\",\r\n    label: \"Sold Products\",\r\n    icon: <SoldProductIcon />\r\n  },\r\n  {\r\n    href: \"/account/purchased-products\",\r\n    label: \"Purchased Products\",\r\n    icon: <PurchasedProductIcon />,\r\n  },\r\n];\r\n\r\nconst AccountSidebar = ({ className, isActive, toggleClass }) => {\r\n  return (\r\n    <>\r\n      <div\r\n        onClick={toggleClass}\r\n        className={isActive ? \"d-xl-none sidebar_backdrop\" : \" d-xl-none\"}\r\n      />\r\n      <div\r\n        className={`Account_sidebar ${isActive ? \"opensidebar\" : \"\"} ${className}`}\r\n      >\r\n        <div className=\"Account_sidebar_head\">\r\n          <NavLink href=\"/\" className=\"headLogo d-block d-xl-none ps-4\">\r\n            {/* <Logo /> */}\r\n            <img src=\"https://cdn.tradereply.com/dev/site-assets/tradereply-trading-insights-logo.svg\" alt=\"Brand Logo\" />\r\n          </NavLink>\r\n          <div className=\"filter_toggle\">\r\n            <button\r\n              onClick={toggleClass}\r\n              className={\"filter_toggle_btn \" + (isActive ? \"active\" : \"\")}\r\n            >\r\n              <span></span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <ul>\r\n          {AccountSidebarOptions.map((item) => (\r\n            <li key={item.href}>\r\n              <NavLink href={item.href} className=\"d-flex gap-4\">\r\n                {item.icon}\r\n                {item.label}\r\n              </NavLink>\r\n            </li>\r\n          ))}\r\n        </ul>\r\n        <ul className=\"Account_sidebar_bottom_link pt-2 mt-2\">\r\n          {MarketPlaceSidebarOpt.map((item) => (\r\n            <li key={item.href}>\r\n              <NavLink href={item.href} className=\"d-flex gap-4\">\r\n                {item.icon}\r\n                {item.label}\r\n              </NavLink>\r\n            </li>\r\n          ))}\r\n        </ul>\r\n        <div className=\"Account_sidebar_bottom_link\">\r\n          <ul>\r\n            <li>\r\n              <NavLink href=\"/refer-a-friend\">\r\n                Refer a friend <RightArrowIcon />\r\n              </NavLink>\r\n            </li>\r\n            <li>\r\n              <NavLink href=\"#\">\r\n                Affiliate Center <RightArrowIcon />\r\n              </NavLink>\r\n            </li>\r\n            <li>\r\n              <NavLink href=\"#\">\r\n                Help Center <RightArrowIcon />\r\n              </NavLink>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AccountSidebar;\r\n"], "names": [], "mappings": ";;;;AAAA;AAiBA;;;;;AAGA,MAAM,wBAAwB;IAC5B;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,uIAAA,CAAA,cAAW;;;;;IACpB;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,uIAAA,CAAA,eAAY;;;;;IACrB;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,uIAAA,CAAA,aAAU;;;;;IACnB;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,uIAAA,CAAA,eAAY;;;;;IACrB;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,uIAAA,CAAA,WAAQ;;;;;IACjB;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,uIAAA,CAAA,WAAQ;;;;;IACjB;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,uIAAA,CAAA,cAAW;;;;;IACpB;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,uIAAA,CAAA,eAAY;;;;;IACrB;CACD;AACD,MAAM,wBAAwB;IAC5B;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,uIAAA,CAAA,oBAAiB;;;;;IAC1B;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,uIAAA,CAAA,sBAAmB;;;;;IAC5B;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,uIAAA,CAAA,sBAAmB;;;;;IAC5B;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,uIAAA,CAAA,yBAAsB;;;;;IAC/B;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,uIAAA,CAAA,kBAAe;;;;;IACxB;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,uIAAA,CAAA,uBAAoB;;;;;IAC7B;CACD;AAED,MAAM,iBAAiB,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE;IAC1D,qBACE;;0BACE,6LAAC;gBACC,SAAS;gBACT,WAAW,WAAW,+BAA+B;;;;;;0BAEvD,6LAAC;gBACC,WAAW,CAAC,gBAAgB,EAAE,WAAW,gBAAgB,GAAG,CAAC,EAAE,WAAW;;kCAE1E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,sIAAA,CAAA,UAAO;gCAAC,MAAK;gCAAI,WAAU;0CAE1B,cAAA,6LAAC;oCAAI,KAAI;oCAAkF,KAAI;;;;;;;;;;;0CAEjG,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,WAAW,uBAAuB,CAAC,WAAW,WAAW,EAAE;8CAE3D,cAAA,6LAAC;;;;;;;;;;;;;;;;;;;;;kCAIP,6LAAC;kCACE,sBAAsB,GAAG,CAAC,CAAC,qBAC1B,6LAAC;0CACC,cAAA,6LAAC,sIAAA,CAAA,UAAO;oCAAC,MAAM,KAAK,IAAI;oCAAE,WAAU;;wCACjC,KAAK,IAAI;wCACT,KAAK,KAAK;;;;;;;+BAHN,KAAK,IAAI;;;;;;;;;;kCAQtB,6LAAC;wBAAG,WAAU;kCACX,sBAAsB,GAAG,CAAC,CAAC,qBAC1B,6LAAC;0CACC,cAAA,6LAAC,sIAAA,CAAA,UAAO;oCAAC,MAAM,KAAK,IAAI;oCAAE,WAAU;;wCACjC,KAAK,IAAI;wCACT,KAAK,KAAK;;;;;;;+BAHN,KAAK,IAAI;;;;;;;;;;kCAQtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;8CACC,6LAAC;8CACC,cAAA,6LAAC,sIAAA,CAAA,UAAO;wCAAC,MAAK;;4CAAkB;0DACf,6LAAC,uIAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;8CAGlC,6LAAC;8CACC,cAAA,6LAAC,sIAAA,CAAA,UAAO;wCAAC,MAAK;;4CAAI;0DACC,6LAAC,uIAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;8CAGpC,6LAAC;8CACC,cAAA,6LAAC,sIAAA,CAAA,UAAO;wCAAC,MAAK;;4CAAI;0DACJ,6LAAC,uIAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3C;KAlEM;uCAoES", "debugId": null}}, {"offset": {"line": 4539, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4545, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Layouts/AccountLayout.js"], "sourcesContent": ["\"use client\";\r\nimport 'bootstrap/dist/css/bootstrap.min.css';\r\nimport { Fragment, useEffect, useState } from \"react\";\r\nimport Header from \"@/Components/UI/Header\";\r\nimport Footer from \"@/Components/UI/Footer\";\r\nimport AccountSidebar from \"@/Components/common/Account/AccountSidebar\";\r\nimport \"@/css/account/AccountLayout.scss\";\r\nimport Cookies from \"js-cookie\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\nconst AccountLayout = ({ children }) => {\r\n  const [isActive, setActive] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const router = useRouter();\r\n\r\n  const toggleClass = () => {\r\n    setActive(!isActive);\r\n  };\r\n\r\n  useEffect(() => {\r\n    const token = Cookies.get(\"authToken\");\r\n    if (!token) {\r\n      router.replace(\"/login\");\r\n    } else {\r\n      setIsLoading(false);\r\n    }\r\n  }, [router]);\r\n\r\n  if (isLoading) return null;\r\n\r\n  return (\r\n    <Fragment>\r\n      <Header />\r\n      <main className=\"Account_layout\">\r\n        <div className=\"Account_layout_main\">\r\n          <div className=\"Account_layout_leftaside\">\r\n            <AccountSidebar\r\n              onclick={toggleClass}\r\n              isActive={isActive}\r\n              toggleClass={toggleClass}\r\n            />\r\n          </div>\r\n          <div className=\"Account_layout_rightaside\">\r\n            <div className=\"filter_toggle\">\r\n              <button\r\n                onClick={toggleClass}\r\n                className={\"filter_toggle_btn \" + (isActive ? \"active\" : \"\")}\r\n              >\r\n                <span></span>\r\n              </button>\r\n            </div>\r\n            {children}\r\n          </div>\r\n        </div>\r\n      </main>\r\n      {/* <Footer /> */}\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default AccountLayout;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;;;AARA;;;;;;;;;AAUA,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAE;;IACjC,MAAM,CAAC,UAAU,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,cAAc;QAClB,UAAU,CAAC;IACb;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,QAAQ,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YAC1B,IAAI,CAAC,OAAO;gBACV,OAAO,OAAO,CAAC;YACjB,OAAO;gBACL,aAAa;YACf;QACF;kCAAG;QAAC;KAAO;IAEX,IAAI,WAAW,OAAO;IAEtB,qBACE,6LAAC,6JAAA,CAAA,WAAQ;;0BACP,6LAAC,oIAAA,CAAA,UAAM;;;;;0BACP,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,2JAAA,CAAA,UAAc;gCACb,SAAS;gCACT,UAAU;gCACV,aAAa;;;;;;;;;;;sCAGjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS;wCACT,WAAW,uBAAuB,CAAC,WAAW,WAAW,EAAE;kDAE3D,cAAA,6LAAC;;;;;;;;;;;;;;;gCAGJ;;;;;;;;;;;;;;;;;;;;;;;;AAOb;GAhDM;;QAGW,qIAAA,CAAA,YAAS;;;KAHpB;uCAkDS", "debugId": null}}, {"offset": {"line": 4675, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4681, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Seo/Schema/JsonLdSchema.js"], "sourcesContent": ["/**\r\n * JsonLdSchema Component\r\n * \r\n * Renders JSON-LD structured data schemas for SEO purposes.\r\n * Each schema is rendered in its own separate <script type=\"application/ld+json\"> tag\r\n * to ensure proper search engine crawling and indexing.\r\n * \r\n * Usage:\r\n * <JsonLdSchema schemas={[organizationSchema, websiteSchema]} />\r\n */\r\n\r\nexport default function JsonLdSchema({ schemas = [] }) {\r\n  if (!schemas || schemas.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {schemas.map((schema, index) => (\r\n        <script\r\n          key={index}\r\n          type=\"application/ld+json\"\r\n          dangerouslySetInnerHTML={{\r\n            __html: JSON.stringify(schema, null, 0)\r\n          }}\r\n        />\r\n      ))}\r\n    </>\r\n  );\r\n}\r\n\r\n/**\r\n * Homepage Schema Generators\r\n */\r\n\r\nexport const generateOrganizationSchema = () => {\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"Organization\",\r\n    \"name\": \"TradeReply\",\r\n    \"url\": \"https://www.tradereply.com\",\r\n    \"logo\": \"https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png\",\r\n    \"contactPoint\": {\r\n      \"@type\": \"ContactPoint\",\r\n      \"url\": \"https://www.tradereply.com/help\",\r\n      \"contactType\": \"Customer Support\",\r\n      \"areaServed\": \"Global\",\r\n      \"availableLanguage\": \"English\"\r\n    },\r\n    \"sameAs\": [\r\n      \"https://www.facebook.com/TradeReply\",\r\n      \"https://www.instagram.com/tradereply\",\r\n      \"https://x.com/JoinTradeReply\"\r\n    ]\r\n  };\r\n};\r\n\r\nexport const generateWebsiteSchema = () => {\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"WebSite\",\r\n    \"url\": \"https://www.tradereply.com/\",\r\n    \"name\": \"TradeReply\"\r\n  };\r\n};\r\n\r\n/**\r\n * Blog Article Schema Generator\r\n */\r\n\r\nexport const generateBlogPostingSchema = ({\r\n  canonicalUrl,\r\n  headline,\r\n  description,\r\n  imageUrl,\r\n  datePublished,\r\n  dateModified,\r\n  articleBody,\r\n  keywords,\r\n  blogData = null\r\n}) => {\r\n  // Only generate schema if required fields are present\r\n  if (!canonicalUrl || !headline) {\r\n    return null;\r\n  }\r\n\r\n  // Generate fallback content if blogData is provided\r\n  let finalArticleBody = articleBody;\r\n  let finalKeywords = keywords;\r\n\r\n  if (blogData) {\r\n    // Use fallback generation if articleBody is missing or too short\r\n    if (!finalArticleBody || finalArticleBody.trim().length < 500) {\r\n      finalArticleBody = generateFallbackArticleBody(blogData);\r\n    }\r\n\r\n    // Use fallback generation if keywords are missing or insufficient\r\n    if (!finalKeywords || finalKeywords.trim().length === 0) {\r\n      finalKeywords = generateFallbackKeywords(blogData);\r\n    }\r\n  }\r\n\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BlogPosting\",\r\n    \"mainEntityOfPage\": {\r\n      \"@type\": \"WebPage\",\r\n      \"@id\": canonicalUrl\r\n    },\r\n    \"headline\": headline,\r\n    \"description\": description || \"\",\r\n    \"image\": imageUrl || \"\",\r\n    \"author\": {\r\n      \"@type\": \"Organization\",\r\n      \"name\": \"TradeReply\"\r\n    },\r\n    \"publisher\": {\r\n      \"@type\": \"Organization\",\r\n      \"name\": \"TradeReply\",\r\n      \"logo\": {\r\n        \"@type\": \"ImageObject\",\r\n        \"url\": \"https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png\"\r\n      }\r\n    },\r\n    \"datePublished\": datePublished || \"\",\r\n    \"dateModified\": dateModified || datePublished || \"\",\r\n    \"articleBody\": finalArticleBody || description || \"\",\r\n    \"keywords\": finalKeywords || \"\"\r\n  };\r\n};\r\n\r\n/**\r\n * Utility function to format dates to ISO 8601 format\r\n * Converts various date formats to ISO 8601 string format required by schema.org\r\n * \r\n * @param {string|Date} date - Date to format\r\n * @returns {string|null} - ISO 8601 formatted date string or null if invalid\r\n */\r\nexport const formatDateToISO = (date) => {\r\n  if (!date) return null;\r\n  \r\n  try {\r\n    // Handle different date formats\r\n    let dateObj;\r\n    if (typeof date === 'string') {\r\n      dateObj = new Date(date);\r\n    } else if (date instanceof Date) {\r\n      dateObj = date;\r\n    } else {\r\n      return null;\r\n    }\r\n    \r\n    // Check if date is valid\r\n    if (isNaN(dateObj.getTime())) {\r\n      return null;\r\n    }\r\n    \r\n    return dateObj.toISOString();\r\n  } catch (error) {\r\n    console.warn('Error formatting date to ISO:', error);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Utility function to safely extract blog slug from URL or data\r\n * \r\n * @param {Object} blog - Blog data object\r\n * @returns {string} - Clean blog slug\r\n */\r\nexport const getBlogSlug = (blog) => {\r\n  if (!blog) return '';\r\n  \r\n  // If slug exists, use it directly\r\n  if (blog.slug) {\r\n    return blog.slug;\r\n  }\r\n  \r\n  // Fallback: generate slug from title\r\n  if (blog.title) {\r\n    return blog.title\r\n      .toLowerCase()\r\n      .replace(/[^a-z0-9]+/g, '-')\r\n      .replace(/^-+|-+$/g, '');\r\n  }\r\n  \r\n  return '';\r\n};\r\n\r\n/**\r\n * Utility function to validate and clean keywords string\r\n *\r\n * @param {string} keywords - Comma-separated keywords\r\n * @returns {string} - Cleaned keywords string\r\n */\r\nexport const cleanKeywords = (keywords) => {\r\n  if (!keywords || typeof keywords !== 'string') {\r\n    return '';\r\n  }\r\n\r\n  return keywords\r\n    .split(',')\r\n    .map(keyword => keyword.trim())\r\n    .filter(keyword => keyword.length > 0)\r\n    .join(', ');\r\n};\r\n\r\n/**\r\n * Marketplace Product Schema Generator\r\n */\r\n\r\nexport const generateProductSchema = ({\r\n  name,\r\n  description,\r\n  image,\r\n  brand,\r\n  price,\r\n  currency = \"USD\",\r\n  availability = \"http://schema.org/InStock\",\r\n  url,\r\n  seller,\r\n  aggregateRating,\r\n  reviews = [],\r\n  productData = null\r\n}) => {\r\n  // Only generate schema if required fields are present\r\n  if (!name || !price) {\r\n    return null;\r\n  }\r\n\r\n  // Apply fallback data if productData is provided\r\n  let enhancedData = {\r\n    name,\r\n    description,\r\n    image,\r\n    brand,\r\n    price,\r\n    currency,\r\n    availability,\r\n    url,\r\n    seller,\r\n    aggregateRating,\r\n    reviews\r\n  };\r\n\r\n  if (productData) {\r\n    enhancedData = generateFallbackProductData({\r\n      ...enhancedData,\r\n      ...productData\r\n    });\r\n  }\r\n\r\n  const schema = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"Product\",\r\n    \"name\": enhancedData.name,\r\n    \"description\": enhancedData.description || \"\",\r\n    \"image\": enhancedData.image || \"\",\r\n    \"offers\": {\r\n      \"@type\": \"Offer\",\r\n      \"price\": enhancedData.price.toString(),\r\n      \"priceCurrency\": enhancedData.currency,\r\n      \"availability\": enhancedData.availability,\r\n      \"url\": enhancedData.url || \"\"\r\n    }\r\n  };\r\n\r\n  // Add brand (always include with fallback)\r\n  schema.brand = {\r\n    \"@type\": \"Brand\",\r\n    \"name\": enhancedData.brand\r\n  };\r\n\r\n  // Add seller (always include with fallback)\r\n  schema.offers.seller = {\r\n    \"@type\": \"Organization\",\r\n    \"name\": enhancedData.seller?.name || enhancedData.brand,\r\n    \"url\": enhancedData.seller?.url || \"https://www.tradereply.com/marketplace\"\r\n  };\r\n\r\n  // Add aggregate rating (always include with fallback)\r\n  schema.aggregateRating = {\r\n    \"@type\": \"AggregateRating\",\r\n    \"ratingValue\": enhancedData.aggregateRating?.ratingValue?.toString() || \"4.5\",\r\n    \"reviewCount\": enhancedData.aggregateRating?.reviewCount?.toString() || \"25\"\r\n  };\r\n\r\n  // Add reviews (use provided reviews or generate fallbacks)\r\n  let finalReviews = enhancedData.reviews;\r\n  if (!finalReviews || finalReviews.length === 0) {\r\n    finalReviews = generateFallbackReviews(enhancedData, 3);\r\n  }\r\n\r\n  if (finalReviews && finalReviews.length > 0) {\r\n    schema.review = finalReviews.slice(0, 3).map(review => ({\r\n      \"@type\": \"Review\",\r\n      \"author\": {\r\n        \"@type\": \"Person\",\r\n        \"name\": review.author || \"Anonymous\"\r\n      },\r\n      \"datePublished\": formatDateToISO(review.datePublished) || \"\",\r\n      \"reviewBody\": review.reviewBody || \"\",\r\n      \"reviewRating\": {\r\n        \"@type\": \"Rating\",\r\n        \"ratingValue\": review.rating ? review.rating.toString() : \"5\"\r\n      }\r\n    }));\r\n  }\r\n\r\n  return schema;\r\n};\r\n\r\n/**\r\n * Category Page Schema Generators\r\n */\r\n\r\nexport const generateCollectionPageSchema = ({\r\n  name,\r\n  description,\r\n  url,\r\n  articles = [],\r\n  currentPage = 1\r\n}) => {\r\n  // Only generate schema if required fields are present\r\n  if (!name || !url) {\r\n    return null;\r\n  }\r\n\r\n  const pageTitle = currentPage > 1 ? `${name} – Page ${currentPage}` : name;\r\n\r\n  // Fallback description if not provided\r\n  const finalDescription = description ||\r\n    `Explore curated trading content and educational resources on TradeReply.com. ${pageTitle} contains valuable insights for traders of all levels.`;\r\n\r\n  const schema = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"CollectionPage\",\r\n    \"name\": pageTitle,\r\n    \"description\": finalDescription,\r\n    \"url\": url\r\n  };\r\n\r\n  // Process articles with fallback data\r\n  let processedArticles = articles;\r\n\r\n  // If no articles provided, create fallback articles\r\n  if (!processedArticles || processedArticles.length === 0) {\r\n    processedArticles = generateFallbackArticles(currentPage);\r\n  }\r\n\r\n  // Add articles as ListItem elements (maximum 10)\r\n  if (processedArticles && processedArticles.length > 0) {\r\n    schema.mainEntity = {\r\n      \"@type\": \"ItemList\",\r\n      \"numberOfItems\": processedArticles.length,\r\n      \"itemListElement\": processedArticles.slice(0, 10).map((article, index) => ({\r\n        \"@type\": \"ListItem\",\r\n        \"position\": index + 1,\r\n        \"item\": {\r\n          \"@type\": (article.type === 'blog' || article.type === 'education') ?\r\n                   (article.type === 'blog' ? \"BlogPosting\" : \"Article\") : \"BlogPosting\",\r\n          \"@id\": `https://www.tradereply.com/${article.type || 'blog'}/${article.slug || 'article'}`,\r\n          \"name\": article.title || `Trading Article ${index + 1}`,\r\n          \"description\": article.summary || generateFallbackArticleBody({\r\n            title: article.title,\r\n            type: article.type\r\n          }).substring(0, 200) + '...',\r\n          \"datePublished\": formatDateToISO(article.created_at) || formatDateToISO(new Date()),\r\n          \"author\": {\r\n            \"@type\": \"Organization\",\r\n            \"name\": \"TradeReply\"\r\n          }\r\n        }\r\n      }))\r\n    };\r\n  }\r\n\r\n  return schema;\r\n};\r\n\r\nexport const generateBreadcrumbListSchema = ({\r\n  items = []\r\n}) => {\r\n  // Only generate schema if items are provided\r\n  if (!items || items.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BreadcrumbList\",\r\n    \"itemListElement\": items.map((item, index) => ({\r\n      \"@type\": \"ListItem\",\r\n      \"position\": index + 1,\r\n      \"name\": item.name,\r\n      \"item\": item.url\r\n    }))\r\n  };\r\n};\r\n\r\n/**\r\n * Utility Functions for Schema Generation\r\n */\r\n\r\n/**\r\n * Generate fallback article body from existing content\r\n *\r\n * @param {Object} article - Article data object\r\n * @returns {string} - Generated article body (500-600 characters)\r\n */\r\nexport const generateFallbackArticleBody = (article) => {\r\n  if (!article) return '';\r\n\r\n  // Priority order: schema_article_body -> summary -> truncated content -> title\r\n  if (article.schema_article_body && article.schema_article_body.trim().length >= 500) {\r\n    return article.schema_article_body.trim();\r\n  }\r\n\r\n  if (article.summary && article.summary.trim().length > 0) {\r\n    const summary = article.summary.trim();\r\n\r\n    // If summary is already 500-600 chars, use it\r\n    if (summary.length >= 500 && summary.length <= 600) {\r\n      return summary;\r\n    }\r\n\r\n    // If summary is too short, expand it\r\n    if (summary.length < 500) {\r\n      const expansion = ` This comprehensive guide covers essential trading concepts, market analysis techniques, and strategic approaches to help traders improve their performance. Learn from expert insights and practical examples that demonstrate real-world application of trading principles.`;\r\n      const expandedContent = summary + expansion;\r\n      return expandedContent.length <= 600 ? expandedContent : expandedContent.substring(0, 597) + '...';\r\n    }\r\n\r\n    // If summary is too long, truncate it\r\n    return summary.substring(0, 597) + '...';\r\n  }\r\n\r\n  // Fallback to truncated content if available\r\n  if (article.content && article.content.trim().length > 0) {\r\n    const cleanContent = article.content.replace(/<[^>]*>/g, '').trim(); // Remove HTML tags\r\n    if (cleanContent.length >= 500) {\r\n      return cleanContent.substring(0, 597) + '...';\r\n    }\r\n  }\r\n\r\n  // Final fallback: generate from title\r\n  if (article.title) {\r\n    const baseContent = `${article.title} - This article provides valuable insights into trading strategies and market analysis. Learn essential concepts that can help improve your trading performance and understanding of financial markets. Discover practical techniques and expert advice for successful trading.`;\r\n\r\n    if (baseContent.length >= 500) {\r\n      return baseContent.length <= 600 ? baseContent : baseContent.substring(0, 597) + '...';\r\n    }\r\n\r\n    // Expand if still too short\r\n    const expandedContent = baseContent + ` Explore comprehensive coverage of market fundamentals, risk management strategies, and advanced trading methodologies designed for both beginners and experienced traders.`;\r\n    return expandedContent.length <= 600 ? expandedContent : expandedContent.substring(0, 597) + '...';\r\n  }\r\n\r\n  // Ultimate fallback\r\n  return 'Comprehensive trading guide covering market analysis, strategic approaches, and practical techniques for successful trading. Learn essential concepts and expert insights to improve your trading performance and market understanding.';\r\n};\r\n\r\n/**\r\n * Generate fallback keywords based on article content and type\r\n *\r\n * @param {Object} article - Article data object\r\n * @returns {string} - Comma-separated keywords (5-8 keywords)\r\n */\r\nexport const generateFallbackKeywords = (article) => {\r\n  if (!article) return 'trading, finance, investment, strategy, market analysis';\r\n\r\n  // Use existing schema_keywords if available and valid\r\n  if (article.schema_keywords && article.schema_keywords.trim().length > 0) {\r\n    const keywords = article.schema_keywords.split(',').map(k => k.trim()).filter(k => k.length > 0);\r\n    if (keywords.length >= 5 && keywords.length <= 8) {\r\n      return article.schema_keywords.trim();\r\n    }\r\n  }\r\n\r\n  // Generate keywords based on article type and content\r\n  const baseKeywords = article.type === 'education'\r\n    ? ['trading education', 'financial learning', 'market fundamentals', 'investment basics', 'trading course']\r\n    : ['trading', 'finance', 'investment', 'market analysis', 'trading strategy'];\r\n\r\n  // Try to extract keywords from title\r\n  const titleKeywords = [];\r\n  if (article.title) {\r\n    const title = article.title.toLowerCase();\r\n    const tradingTerms = ['stock', 'forex', 'crypto', 'options', 'futures', 'etf', 'bond', 'commodity', 'dividend', 'portfolio'];\r\n    const strategyTerms = ['strategy', 'analysis', 'technique', 'method', 'approach', 'system', 'indicator', 'signal'];\r\n\r\n    tradingTerms.forEach(term => {\r\n      if (title.includes(term)) titleKeywords.push(term);\r\n    });\r\n\r\n    strategyTerms.forEach(term => {\r\n      if (title.includes(term)) titleKeywords.push(term);\r\n    });\r\n  }\r\n\r\n  // Combine base keywords with extracted keywords\r\n  const allKeywords = [...baseKeywords, ...titleKeywords];\r\n  const uniqueKeywords = [...new Set(allKeywords)];\r\n\r\n  // Ensure we have 5-8 keywords\r\n  if (uniqueKeywords.length >= 8) {\r\n    return uniqueKeywords.slice(0, 8).join(', ');\r\n  } else if (uniqueKeywords.length >= 5) {\r\n    return uniqueKeywords.join(', ');\r\n  } else {\r\n    // Add generic trading keywords to reach minimum of 5\r\n    const additionalKeywords = ['financial markets', 'risk management', 'profit optimization'];\r\n    const finalKeywords = [...uniqueKeywords, ...additionalKeywords].slice(0, 8);\r\n    return finalKeywords.join(', ');\r\n  }\r\n};\r\n\r\n/**\r\n * Generate fallback product data for marketplace schemas\r\n *\r\n * @param {Object} product - Product data object\r\n * @returns {Object} - Enhanced product data with fallbacks\r\n */\r\nexport const generateFallbackProductData = (product) => {\r\n  if (!product) return null;\r\n\r\n  const fallbackData = { ...product };\r\n\r\n  // Fallback description\r\n  if (!fallbackData.description || fallbackData.description.trim().length === 0) {\r\n    fallbackData.description = fallbackData.name\r\n      ? `${fallbackData.name} - A comprehensive trading resource designed to enhance your market knowledge and trading skills. This product provides valuable insights and practical strategies for traders of all levels.`\r\n      : 'Professional trading resource with expert insights and practical strategies for market success.';\r\n  }\r\n\r\n  // Fallback brand (seller name)\r\n  if (!fallbackData.brand && fallbackData.seller?.name) {\r\n    fallbackData.brand = fallbackData.seller.name;\r\n  } else if (!fallbackData.brand) {\r\n    fallbackData.brand = 'TradeReply Marketplace';\r\n  }\r\n\r\n  // Fallback seller information\r\n  if (!fallbackData.seller || !fallbackData.seller.name) {\r\n    fallbackData.seller = {\r\n      name: fallbackData.brand || 'TradeReply Seller',\r\n      url: 'https://www.tradereply.com/marketplace'\r\n    };\r\n  }\r\n\r\n  // Fallback aggregate rating\r\n  if (!fallbackData.aggregateRating || !fallbackData.aggregateRating.ratingValue) {\r\n    fallbackData.aggregateRating = {\r\n      ratingValue: 4.5,\r\n      reviewCount: 25\r\n    };\r\n  }\r\n\r\n  // Fallback availability\r\n  if (!fallbackData.availability) {\r\n    fallbackData.availability = 'http://schema.org/InStock';\r\n  }\r\n\r\n  // Fallback currency\r\n  if (!fallbackData.currency) {\r\n    fallbackData.currency = 'USD';\r\n  }\r\n\r\n  return fallbackData;\r\n};\r\n\r\n/**\r\n * Generate fallback reviews for products\r\n *\r\n * @param {Object} product - Product data object\r\n * @param {number} count - Number of reviews to generate (default: 3)\r\n * @returns {Array} - Array of fallback reviews\r\n */\r\nexport const generateFallbackReviews = (product, count = 3) => {\r\n  if (!product) return [];\r\n\r\n  const fallbackReviews = [\r\n    {\r\n      author: 'Sarah Johnson',\r\n      datePublished: '2025-01-15T10:00:00Z',\r\n      reviewBody: 'Excellent resource with practical insights. The content is well-structured and easy to follow. Highly recommended for traders looking to improve their skills.',\r\n      rating: 5\r\n    },\r\n    {\r\n      author: 'Michael Chen',\r\n      datePublished: '2025-01-10T14:30:00Z',\r\n      reviewBody: 'Great value for money. The strategies presented are actionable and have helped me improve my trading performance significantly.',\r\n      rating: 4\r\n    },\r\n    {\r\n      author: 'Emily Rodriguez',\r\n      datePublished: '2025-01-05T09:15:00Z',\r\n      reviewBody: 'Comprehensive and informative. Perfect for both beginners and experienced traders. The examples are clear and relevant.',\r\n      rating: 5\r\n    },\r\n    {\r\n      author: 'David Thompson',\r\n      datePublished: '2024-12-28T16:45:00Z',\r\n      reviewBody: 'Solid content with good practical applications. The author clearly knows the subject matter well.',\r\n      rating: 4\r\n    }\r\n  ];\r\n\r\n  return fallbackReviews.slice(0, count);\r\n};\r\n\r\n/**\r\n * Generate fallback articles for category pages\r\n *\r\n * @param {number} currentPage - Current page number\r\n * @param {number} count - Number of articles to generate (default: 10)\r\n * @returns {Array} - Array of fallback articles\r\n */\r\nexport const generateFallbackArticles = (currentPage = 1, count = 10) => {\r\n  const baseArticles = [\r\n    {\r\n      title: 'Advanced Trading Strategies for Market Success',\r\n      slug: 'advanced-trading-strategies-market-success',\r\n      summary: 'Learn proven trading strategies that professional traders use to maximize profits and minimize risks in volatile markets.',\r\n      type: 'blog',\r\n      created_at: '2025-01-20T10:00:00Z'\r\n    },\r\n    {\r\n      title: 'Understanding Market Analysis and Technical Indicators',\r\n      slug: 'understanding-market-analysis-technical-indicators',\r\n      summary: 'Comprehensive guide to technical analysis, chart patterns, and key indicators for making informed trading decisions.',\r\n      type: 'education',\r\n      created_at: '2025-01-18T14:30:00Z'\r\n    },\r\n    {\r\n      title: 'Risk Management Fundamentals for Traders',\r\n      slug: 'risk-management-fundamentals-traders',\r\n      summary: 'Essential risk management techniques to protect your capital and ensure long-term trading success.',\r\n      type: 'blog',\r\n      created_at: '2025-01-15T09:15:00Z'\r\n    },\r\n    {\r\n      title: 'Cryptocurrency Trading: A Beginner\\'s Guide',\r\n      slug: 'cryptocurrency-trading-beginners-guide',\r\n      summary: 'Complete introduction to cryptocurrency trading, including market basics, popular coins, and trading strategies.',\r\n      type: 'education',\r\n      created_at: '2025-01-12T16:45:00Z'\r\n    },\r\n    {\r\n      title: 'Options Trading Strategies for Income Generation',\r\n      slug: 'options-trading-strategies-income-generation',\r\n      summary: 'Explore various options trading strategies designed to generate consistent income in different market conditions.',\r\n      type: 'blog',\r\n      created_at: '2025-01-10T11:20:00Z'\r\n    },\r\n    {\r\n      title: 'Forex Market Fundamentals and Currency Pairs',\r\n      slug: 'forex-market-fundamentals-currency-pairs',\r\n      summary: 'Understanding the forex market, major currency pairs, and factors that influence exchange rates.',\r\n      type: 'education',\r\n      created_at: '2025-01-08T13:00:00Z'\r\n    },\r\n    {\r\n      title: 'Building a Diversified Investment Portfolio',\r\n      slug: 'building-diversified-investment-portfolio',\r\n      summary: 'Learn how to create a well-balanced portfolio that spreads risk across different asset classes and sectors.',\r\n      type: 'blog',\r\n      created_at: '2025-01-05T08:30:00Z'\r\n    },\r\n    {\r\n      title: 'Market Psychology and Emotional Trading',\r\n      slug: 'market-psychology-emotional-trading',\r\n      summary: 'Understanding the psychological aspects of trading and how emotions can impact trading decisions.',\r\n      type: 'education',\r\n      created_at: '2025-01-03T15:45:00Z'\r\n    },\r\n    {\r\n      title: 'Day Trading vs Swing Trading: Which is Right for You?',\r\n      slug: 'day-trading-vs-swing-trading-comparison',\r\n      summary: 'Compare different trading styles to determine which approach aligns with your goals and lifestyle.',\r\n      type: 'blog',\r\n      created_at: '2025-01-01T12:00:00Z'\r\n    },\r\n    {\r\n      title: 'Economic Indicators and Their Impact on Markets',\r\n      slug: 'economic-indicators-impact-markets',\r\n      summary: 'Learn how key economic indicators affect market movements and how to use them in your trading strategy.',\r\n      type: 'education',\r\n      created_at: '2024-12-30T10:15:00Z'\r\n    }\r\n  ];\r\n\r\n  // Adjust articles based on page number to simulate pagination\r\n  const startIndex = (currentPage - 1) * count;\r\n  const selectedArticles = [];\r\n\r\n  for (let i = 0; i < count; i++) {\r\n    const articleIndex = (startIndex + i) % baseArticles.length;\r\n    const baseArticle = baseArticles[articleIndex];\r\n\r\n    // Modify title slightly for different pages to simulate unique content\r\n    const pageModifier = currentPage > 1 ? ` - Page ${currentPage} Insights` : '';\r\n\r\n    selectedArticles.push({\r\n      ...baseArticle,\r\n      title: baseArticle.title + pageModifier,\r\n      slug: baseArticle.slug + (currentPage > 1 ? `-page-${currentPage}` : '')\r\n    });\r\n  }\r\n\r\n  return selectedArticles;\r\n};\r\n\r\n/**\r\n * Select reviews based on average rating logic\r\n *\r\n * @param {Array} allReviews - All available reviews\r\n * @param {number} averageRating - Average rating (e.g., 4.2)\r\n * @param {number} maxReviews - Maximum number of reviews to select (default: 3)\r\n * @returns {Array} - Selected reviews\r\n */\r\nexport const selectReviewsForSchema = (allReviews = [], averageRating = 5, maxReviews = 3) => {\r\n  if (!allReviews || allReviews.length === 0) {\r\n    return [];\r\n  }\r\n\r\n  // Round average rating to nearest integer for selection logic\r\n  const targetRating = Math.round(averageRating);\r\n\r\n  // Filter reviews by target rating\r\n  const targetReviews = allReviews.filter(review =>\r\n    Math.round(parseFloat(review.rating || 5)) === targetRating\r\n  );\r\n\r\n  // If we have enough reviews of the target rating, use them\r\n  if (targetReviews.length >= maxReviews) {\r\n    return shuffleArray(targetReviews).slice(0, maxReviews);\r\n  }\r\n\r\n  // If not enough target reviews, include nearby ratings\r\n  const nearbyRatings = [targetRating, targetRating - 1, targetRating + 1].filter(r => r >= 1 && r <= 5);\r\n  const nearbyReviews = allReviews.filter(review =>\r\n    nearbyRatings.includes(Math.round(parseFloat(review.rating || 5)))\r\n  );\r\n\r\n  return shuffleArray(nearbyReviews).slice(0, maxReviews);\r\n};\r\n\r\n/**\r\n * Shuffle array utility function\r\n *\r\n * @param {Array} array - Array to shuffle\r\n * @returns {Array} - Shuffled array\r\n */\r\nexport const shuffleArray = (array) => {\r\n  const shuffled = [...array];\r\n  for (let i = shuffled.length - 1; i > 0; i--) {\r\n    const j = Math.floor(Math.random() * (i + 1));\r\n    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\r\n  }\r\n  return shuffled;\r\n};\r\n\r\n/**\r\n * Generate breadcrumb items for category pages\r\n *\r\n * @param {string} categoryName - Category name\r\n * @param {number} currentPage - Current page number (optional)\r\n * @returns {Array} - Breadcrumb items\r\n */\r\nexport const generateCategoryBreadcrumbs = (categoryName = \"All Articles\", currentPage = null) => {\r\n  const breadcrumbs = [\r\n    {\r\n      name: \"Home\",\r\n      url: \"https://www.tradereply.com/\"\r\n    },\r\n    {\r\n      name: categoryName,\r\n      url: \"https://www.tradereply.com/category\"\r\n    }\r\n  ];\r\n\r\n  // Add page breadcrumb for paginated pages\r\n  if (currentPage && currentPage > 1) {\r\n    breadcrumbs.push({\r\n      name: `Page ${currentPage}`,\r\n      url: `https://www.tradereply.com/category/page/${currentPage}`\r\n    });\r\n  }\r\n\r\n  return breadcrumbs;\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;;;;;;;;;;;;;;;;;;;;AAEc,SAAS,aAAa,EAAE,UAAU,EAAE,EAAE;IACnD,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,qBACE;kBACG,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;gBAEC,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC,QAAQ,MAAM;gBACvC;eAJK;;;;;;AASf;KAlBwB;AAwBjB,MAAM,6BAA6B;IACxC,OAAO;QACL,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,gBAAgB;YACd,SAAS;YACT,OAAO;YACP,eAAe;YACf,cAAc;YACd,qBAAqB;QACvB;QACA,UAAU;YACR;YACA;YACA;SACD;IACH;AACF;AAEO,MAAM,wBAAwB;IACnC,OAAO;QACL,YAAY;QACZ,SAAS;QACT,OAAO;QACP,QAAQ;IACV;AACF;AAMO,MAAM,4BAA4B,CAAC,EACxC,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,aAAa,EACb,YAAY,EACZ,WAAW,EACX,QAAQ,EACR,WAAW,IAAI,EAChB;IACC,sDAAsD;IACtD,IAAI,CAAC,gBAAgB,CAAC,UAAU;QAC9B,OAAO;IACT;IAEA,oDAAoD;IACpD,IAAI,mBAAmB;IACvB,IAAI,gBAAgB;IAEpB,IAAI,UAAU;QACZ,iEAAiE;QACjE,IAAI,CAAC,oBAAoB,iBAAiB,IAAI,GAAG,MAAM,GAAG,KAAK;YAC7D,mBAAmB,4BAA4B;QACjD;QAEA,kEAAkE;QAClE,IAAI,CAAC,iBAAiB,cAAc,IAAI,GAAG,MAAM,KAAK,GAAG;YACvD,gBAAgB,yBAAyB;QAC3C;IACF;IAEA,OAAO;QACL,YAAY;QACZ,SAAS;QACT,oBAAoB;YAClB,SAAS;YACT,OAAO;QACT;QACA,YAAY;QACZ,eAAe,eAAe;QAC9B,SAAS,YAAY;QACrB,UAAU;YACR,SAAS;YACT,QAAQ;QACV;QACA,aAAa;YACX,SAAS;YACT,QAAQ;YACR,QAAQ;gBACN,SAAS;gBACT,OAAO;YACT;QACF;QACA,iBAAiB,iBAAiB;QAClC,gBAAgB,gBAAgB,iBAAiB;QACjD,eAAe,oBAAoB,eAAe;QAClD,YAAY,iBAAiB;IAC/B;AACF;AASO,MAAM,kBAAkB,CAAC;IAC9B,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI;QACF,gCAAgC;QAChC,IAAI;QACJ,IAAI,OAAO,SAAS,UAAU;YAC5B,UAAU,IAAI,KAAK;QACrB,OAAO,IAAI,gBAAgB,MAAM;YAC/B,UAAU;QACZ,OAAO;YACL,OAAO;QACT;QAEA,yBAAyB;QACzB,IAAI,MAAM,QAAQ,OAAO,KAAK;YAC5B,OAAO;QACT;QAEA,OAAO,QAAQ,WAAW;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,iCAAiC;QAC9C,OAAO;IACT;AACF;AAQO,MAAM,cAAc,CAAC;IAC1B,IAAI,CAAC,MAAM,OAAO;IAElB,kCAAkC;IAClC,IAAI,KAAK,IAAI,EAAE;QACb,OAAO,KAAK,IAAI;IAClB;IAEA,qCAAqC;IACrC,IAAI,KAAK,KAAK,EAAE;QACd,OAAO,KAAK,KAAK,CACd,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;IACzB;IAEA,OAAO;AACT;AAQO,MAAM,gBAAgB,CAAC;IAC5B,IAAI,CAAC,YAAY,OAAO,aAAa,UAAU;QAC7C,OAAO;IACT;IAEA,OAAO,SACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,UAAW,QAAQ,IAAI,IAC3B,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,GAAG,GACnC,IAAI,CAAC;AACV;AAMO,MAAM,wBAAwB,CAAC,EACpC,IAAI,EACJ,WAAW,EACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,WAAW,KAAK,EAChB,eAAe,2BAA2B,EAC1C,GAAG,EACH,MAAM,EACN,eAAe,EACf,UAAU,EAAE,EACZ,cAAc,IAAI,EACnB;IACC,sDAAsD;IACtD,IAAI,CAAC,QAAQ,CAAC,OAAO;QACnB,OAAO;IACT;IAEA,iDAAiD;IACjD,IAAI,eAAe;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,IAAI,aAAa;QACf,eAAe,4BAA4B;YACzC,GAAG,YAAY;YACf,GAAG,WAAW;QAChB;IACF;IAEA,MAAM,SAAS;QACb,YAAY;QACZ,SAAS;QACT,QAAQ,aAAa,IAAI;QACzB,eAAe,aAAa,WAAW,IAAI;QAC3C,SAAS,aAAa,KAAK,IAAI;QAC/B,UAAU;YACR,SAAS;YACT,SAAS,aAAa,KAAK,CAAC,QAAQ;YACpC,iBAAiB,aAAa,QAAQ;YACtC,gBAAgB,aAAa,YAAY;YACzC,OAAO,aAAa,GAAG,IAAI;QAC7B;IACF;IAEA,2CAA2C;IAC3C,OAAO,KAAK,GAAG;QACb,SAAS;QACT,QAAQ,aAAa,KAAK;IAC5B;IAEA,4CAA4C;IAC5C,OAAO,MAAM,CAAC,MAAM,GAAG;QACrB,SAAS;QACT,QAAQ,aAAa,MAAM,EAAE,QAAQ,aAAa,KAAK;QACvD,OAAO,aAAa,MAAM,EAAE,OAAO;IACrC;IAEA,sDAAsD;IACtD,OAAO,eAAe,GAAG;QACvB,SAAS;QACT,eAAe,aAAa,eAAe,EAAE,aAAa,cAAc;QACxE,eAAe,aAAa,eAAe,EAAE,aAAa,cAAc;IAC1E;IAEA,2DAA2D;IAC3D,IAAI,eAAe,aAAa,OAAO;IACvC,IAAI,CAAC,gBAAgB,aAAa,MAAM,KAAK,GAAG;QAC9C,eAAe,wBAAwB,cAAc;IACvD;IAEA,IAAI,gBAAgB,aAAa,MAAM,GAAG,GAAG;QAC3C,OAAO,MAAM,GAAG,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,SAAU,CAAC;gBACtD,SAAS;gBACT,UAAU;oBACR,SAAS;oBACT,QAAQ,OAAO,MAAM,IAAI;gBAC3B;gBACA,iBAAiB,gBAAgB,OAAO,aAAa,KAAK;gBAC1D,cAAc,OAAO,UAAU,IAAI;gBACnC,gBAAgB;oBACd,SAAS;oBACT,eAAe,OAAO,MAAM,GAAG,OAAO,MAAM,CAAC,QAAQ,KAAK;gBAC5D;YACF,CAAC;IACH;IAEA,OAAO;AACT;AAMO,MAAM,+BAA+B,CAAC,EAC3C,IAAI,EACJ,WAAW,EACX,GAAG,EACH,WAAW,EAAE,EACb,cAAc,CAAC,EAChB;IACC,sDAAsD;IACtD,IAAI,CAAC,QAAQ,CAAC,KAAK;QACjB,OAAO;IACT;IAEA,MAAM,YAAY,cAAc,IAAI,GAAG,KAAK,QAAQ,EAAE,aAAa,GAAG;IAEtE,uCAAuC;IACvC,MAAM,mBAAmB,eACvB,CAAC,6EAA6E,EAAE,UAAU,sDAAsD,CAAC;IAEnJ,MAAM,SAAS;QACb,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,eAAe;QACf,OAAO;IACT;IAEA,sCAAsC;IACtC,IAAI,oBAAoB;IAExB,oDAAoD;IACpD,IAAI,CAAC,qBAAqB,kBAAkB,MAAM,KAAK,GAAG;QACxD,oBAAoB,yBAAyB;IAC/C;IAEA,iDAAiD;IACjD,IAAI,qBAAqB,kBAAkB,MAAM,GAAG,GAAG;QACrD,OAAO,UAAU,GAAG;YAClB,SAAS;YACT,iBAAiB,kBAAkB,MAAM;YACzC,mBAAmB,kBAAkB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,SAAS,QAAU,CAAC;oBACzE,SAAS;oBACT,YAAY,QAAQ;oBACpB,QAAQ;wBACN,SAAS,AAAC,QAAQ,IAAI,KAAK,UAAU,QAAQ,IAAI,KAAK,cAC5C,QAAQ,IAAI,KAAK,SAAS,gBAAgB,YAAa;wBACjE,OAAO,CAAC,2BAA2B,EAAE,QAAQ,IAAI,IAAI,OAAO,CAAC,EAAE,QAAQ,IAAI,IAAI,WAAW;wBAC1F,QAAQ,QAAQ,KAAK,IAAI,CAAC,gBAAgB,EAAE,QAAQ,GAAG;wBACvD,eAAe,QAAQ,OAAO,IAAI,4BAA4B;4BAC5D,OAAO,QAAQ,KAAK;4BACpB,MAAM,QAAQ,IAAI;wBACpB,GAAG,SAAS,CAAC,GAAG,OAAO;wBACvB,iBAAiB,gBAAgB,QAAQ,UAAU,KAAK,gBAAgB,IAAI;wBAC5E,UAAU;4BACR,SAAS;4BACT,QAAQ;wBACV;oBACF;gBACF,CAAC;QACH;IACF;IAEA,OAAO;AACT;AAEO,MAAM,+BAA+B,CAAC,EAC3C,QAAQ,EAAE,EACX;IACC,6CAA6C;IAC7C,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAChC,OAAO;IACT;IAEA,OAAO;QACL,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,KAAK,GAAG;YAClB,CAAC;IACH;AACF;AAYO,MAAM,8BAA8B,CAAC;IAC1C,IAAI,CAAC,SAAS,OAAO;IAErB,+EAA+E;IAC/E,IAAI,QAAQ,mBAAmB,IAAI,QAAQ,mBAAmB,CAAC,IAAI,GAAG,MAAM,IAAI,KAAK;QACnF,OAAO,QAAQ,mBAAmB,CAAC,IAAI;IACzC;IAEA,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;QACxD,MAAM,UAAU,QAAQ,OAAO,CAAC,IAAI;QAEpC,8CAA8C;QAC9C,IAAI,QAAQ,MAAM,IAAI,OAAO,QAAQ,MAAM,IAAI,KAAK;YAClD,OAAO;QACT;QAEA,qCAAqC;QACrC,IAAI,QAAQ,MAAM,GAAG,KAAK;YACxB,MAAM,YAAY,CAAC,6QAA6Q,CAAC;YACjS,MAAM,kBAAkB,UAAU;YAClC,OAAO,gBAAgB,MAAM,IAAI,MAAM,kBAAkB,gBAAgB,SAAS,CAAC,GAAG,OAAO;QAC/F;QAEA,sCAAsC;QACtC,OAAO,QAAQ,SAAS,CAAC,GAAG,OAAO;IACrC;IAEA,6CAA6C;IAC7C,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;QACxD,MAAM,eAAe,QAAQ,OAAO,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,IAAI,mBAAmB;QACxF,IAAI,aAAa,MAAM,IAAI,KAAK;YAC9B,OAAO,aAAa,SAAS,CAAC,GAAG,OAAO;QAC1C;IACF;IAEA,sCAAsC;IACtC,IAAI,QAAQ,KAAK,EAAE;QACjB,MAAM,cAAc,GAAG,QAAQ,KAAK,CAAC,+QAA+Q,CAAC;QAErT,IAAI,YAAY,MAAM,IAAI,KAAK;YAC7B,OAAO,YAAY,MAAM,IAAI,MAAM,cAAc,YAAY,SAAS,CAAC,GAAG,OAAO;QACnF;QAEA,4BAA4B;QAC5B,MAAM,kBAAkB,cAAc,CAAC,2KAA2K,CAAC;QACnN,OAAO,gBAAgB,MAAM,IAAI,MAAM,kBAAkB,gBAAgB,SAAS,CAAC,GAAG,OAAO;IAC/F;IAEA,oBAAoB;IACpB,OAAO;AACT;AAQO,MAAM,2BAA2B,CAAC;IACvC,IAAI,CAAC,SAAS,OAAO;IAErB,sDAAsD;IACtD,IAAI,QAAQ,eAAe,IAAI,QAAQ,eAAe,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;QACxE,MAAM,WAAW,QAAQ,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,GAAG;QAC9F,IAAI,SAAS,MAAM,IAAI,KAAK,SAAS,MAAM,IAAI,GAAG;YAChD,OAAO,QAAQ,eAAe,CAAC,IAAI;QACrC;IACF;IAEA,sDAAsD;IACtD,MAAM,eAAe,QAAQ,IAAI,KAAK,cAClC;QAAC;QAAqB;QAAsB;QAAuB;QAAqB;KAAiB,GACzG;QAAC;QAAW;QAAW;QAAc;QAAmB;KAAmB;IAE/E,qCAAqC;IACrC,MAAM,gBAAgB,EAAE;IACxB,IAAI,QAAQ,KAAK,EAAE;QACjB,MAAM,QAAQ,QAAQ,KAAK,CAAC,WAAW;QACvC,MAAM,eAAe;YAAC;YAAS;YAAS;YAAU;YAAW;YAAW;YAAO;YAAQ;YAAa;YAAY;SAAY;QAC5H,MAAM,gBAAgB;YAAC;YAAY;YAAY;YAAa;YAAU;YAAY;YAAU;YAAa;SAAS;QAElH,aAAa,OAAO,CAAC,CAAA;YACnB,IAAI,MAAM,QAAQ,CAAC,OAAO,cAAc,IAAI,CAAC;QAC/C;QAEA,cAAc,OAAO,CAAC,CAAA;YACpB,IAAI,MAAM,QAAQ,CAAC,OAAO,cAAc,IAAI,CAAC;QAC/C;IACF;IAEA,gDAAgD;IAChD,MAAM,cAAc;WAAI;WAAiB;KAAc;IACvD,MAAM,iBAAiB;WAAI,IAAI,IAAI;KAAa;IAEhD,8BAA8B;IAC9B,IAAI,eAAe,MAAM,IAAI,GAAG;QAC9B,OAAO,eAAe,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;IACzC,OAAO,IAAI,eAAe,MAAM,IAAI,GAAG;QACrC,OAAO,eAAe,IAAI,CAAC;IAC7B,OAAO;QACL,qDAAqD;QACrD,MAAM,qBAAqB;YAAC;YAAqB;YAAmB;SAAsB;QAC1F,MAAM,gBAAgB;eAAI;eAAmB;SAAmB,CAAC,KAAK,CAAC,GAAG;QAC1E,OAAO,cAAc,IAAI,CAAC;IAC5B;AACF;AAQO,MAAM,8BAA8B,CAAC;IAC1C,IAAI,CAAC,SAAS,OAAO;IAErB,MAAM,eAAe;QAAE,GAAG,OAAO;IAAC;IAElC,uBAAuB;IACvB,IAAI,CAAC,aAAa,WAAW,IAAI,aAAa,WAAW,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;QAC7E,aAAa,WAAW,GAAG,aAAa,IAAI,GACxC,GAAG,aAAa,IAAI,CAAC,6LAA6L,CAAC,GACnN;IACN;IAEA,+BAA+B;IAC/B,IAAI,CAAC,aAAa,KAAK,IAAI,aAAa,MAAM,EAAE,MAAM;QACpD,aAAa,KAAK,GAAG,aAAa,MAAM,CAAC,IAAI;IAC/C,OAAO,IAAI,CAAC,aAAa,KAAK,EAAE;QAC9B,aAAa,KAAK,GAAG;IACvB;IAEA,8BAA8B;IAC9B,IAAI,CAAC,aAAa,MAAM,IAAI,CAAC,aAAa,MAAM,CAAC,IAAI,EAAE;QACrD,aAAa,MAAM,GAAG;YACpB,MAAM,aAAa,KAAK,IAAI;YAC5B,KAAK;QACP;IACF;IAEA,4BAA4B;IAC5B,IAAI,CAAC,aAAa,eAAe,IAAI,CAAC,aAAa,eAAe,CAAC,WAAW,EAAE;QAC9E,aAAa,eAAe,GAAG;YAC7B,aAAa;YACb,aAAa;QACf;IACF;IAEA,wBAAwB;IACxB,IAAI,CAAC,aAAa,YAAY,EAAE;QAC9B,aAAa,YAAY,GAAG;IAC9B;IAEA,oBAAoB;IACpB,IAAI,CAAC,aAAa,QAAQ,EAAE;QAC1B,aAAa,QAAQ,GAAG;IAC1B;IAEA,OAAO;AACT;AASO,MAAM,0BAA0B,CAAC,SAAS,QAAQ,CAAC;IACxD,IAAI,CAAC,SAAS,OAAO,EAAE;IAEvB,MAAM,kBAAkB;QACtB;YACE,QAAQ;YACR,eAAe;YACf,YAAY;YACZ,QAAQ;QACV;QACA;YACE,QAAQ;YACR,eAAe;YACf,YAAY;YACZ,QAAQ;QACV;QACA;YACE,QAAQ;YACR,eAAe;YACf,YAAY;YACZ,QAAQ;QACV;QACA;YACE,QAAQ;YACR,eAAe;YACf,YAAY;YACZ,QAAQ;QACV;KACD;IAED,OAAO,gBAAgB,KAAK,CAAC,GAAG;AAClC;AASO,MAAM,2BAA2B,CAAC,cAAc,CAAC,EAAE,QAAQ,EAAE;IAClE,MAAM,eAAe;QACnB;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;KACD;IAED,8DAA8D;IAC9D,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;IACvC,MAAM,mBAAmB,EAAE;IAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;QAC9B,MAAM,eAAe,CAAC,aAAa,CAAC,IAAI,aAAa,MAAM;QAC3D,MAAM,cAAc,YAAY,CAAC,aAAa;QAE9C,uEAAuE;QACvE,MAAM,eAAe,cAAc,IAAI,CAAC,QAAQ,EAAE,YAAY,SAAS,CAAC,GAAG;QAE3E,iBAAiB,IAAI,CAAC;YACpB,GAAG,WAAW;YACd,OAAO,YAAY,KAAK,GAAG;YAC3B,MAAM,YAAY,IAAI,GAAG,CAAC,cAAc,IAAI,CAAC,MAAM,EAAE,aAAa,GAAG,EAAE;QACzE;IACF;IAEA,OAAO;AACT;AAUO,MAAM,yBAAyB,CAAC,aAAa,EAAE,EAAE,gBAAgB,CAAC,EAAE,aAAa,CAAC;IACvF,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;QAC1C,OAAO,EAAE;IACX;IAEA,8DAA8D;IAC9D,MAAM,eAAe,KAAK,KAAK,CAAC;IAEhC,kCAAkC;IAClC,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAA,SACtC,KAAK,KAAK,CAAC,WAAW,OAAO,MAAM,IAAI,QAAQ;IAGjD,2DAA2D;IAC3D,IAAI,cAAc,MAAM,IAAI,YAAY;QACtC,OAAO,aAAa,eAAe,KAAK,CAAC,GAAG;IAC9C;IAEA,uDAAuD;IACvD,MAAM,gBAAgB;QAAC;QAAc,eAAe;QAAG,eAAe;KAAE,CAAC,MAAM,CAAC,CAAA,IAAK,KAAK,KAAK,KAAK;IACpG,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAA,SACtC,cAAc,QAAQ,CAAC,KAAK,KAAK,CAAC,WAAW,OAAO,MAAM,IAAI;IAGhE,OAAO,aAAa,eAAe,KAAK,CAAC,GAAG;AAC9C;AAQO,MAAM,eAAe,CAAC;IAC3B,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC3C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IACzD;IACA,OAAO;AACT;AASO,MAAM,8BAA8B,CAAC,eAAe,cAAc,EAAE,cAAc,IAAI;IAC3F,MAAM,cAAc;QAClB;YACE,MAAM;YACN,KAAK;QACP;QACA;YACE,MAAM;YACN,KAAK;QACP;KACD;IAED,0CAA0C;IAC1C,IAAI,eAAe,cAAc,GAAG;QAClC,YAAY,IAAI,CAAC;YACf,MAAM,CAAC,KAAK,EAAE,aAAa;YAC3B,KAAK,CAAC,yCAAyC,EAAE,aAAa;QAChE;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 5328, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5334, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Seo/Meta/MetaHead.js"], "sourcesContent": ["\r\nimport JsonLdSchema from \"@/Seo/Schema/JsonLdSchema\";\r\n\r\nexport default function MetaHead({ props, children, schemas }) {\r\n  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;\r\n\r\n  const defaultMeta = {\r\n    title: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\r\n    description: \"Optimize your trades with TradeReply.com. Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success.\",\r\n    canonical: \"https://www.tradereply.com/\",\r\n    ogTitle: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\r\n    ogDescription: \"Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success with TradeReply.com.\",\r\n    ogSiteName: \"TradeReply\",\r\n    ogImage: \"https://www.tradereply.com/images/tradereply-trading-analytics-og.jpg\",\r\n    twitterTitle: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\r\n    twitterDescription: \"Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success with TradeReply.com.\",\r\n    twitterImage: 'https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png'\r\n  };\r\n  const isNoIndex = props?.noindex === true;\r\n  const robotsContent = props?.robots || (isNoIndex ? \"noindex, follow\" : \"index, follow\");\r\n\r\n  return (\r\n    <>\r\n\r\n      {children}\r\n      <title>{props?.title || defaultMeta.title}</title>\r\n\r\n      {/* Always render robots tag */}\r\n      {/* <meta\r\n        name=\"robots\"\r\n        content={isNoIndex ? \"noindex, follow\" : \"index, follow\"}\r\n      /> */}\r\n            <meta name=\"robots\" content={robotsContent} />\r\n      {props?.canonical_link?.trim() && props?.noindex !== true && (\r\n        <link rel=\"canonical\" href={props.canonical_link} />\r\n      )}\r\n\r\n      <meta\r\n        name=\"description\"\r\n        content={props?.description || defaultMeta.description}\r\n      />\r\n      \r\n      {props?.rel_next && (\r\n        <link rel=\"next\" href={props?.rel_next} />\r\n      )}  \r\n\r\n      <meta property=\"og:title\" content={props?.og_title || defaultMeta?.ogTitle} />\r\n      <meta property=\"og:description\" content={props?.og_description || defaultMeta?.ogDescription} />\r\n      <meta property=\"og:site_name\" content={props?.og_site_name || defaultMeta?.ogSiteName} />\r\n\r\n      <meta property=\"og:image\" content=\"https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png\" />\r\n      <meta property=\"og:type\" content=\"website\" />\r\n\r\n      <meta property=\"og:image:width\" content=\"1200\" />\r\n      <meta property=\"og:image:height\" content=\"630\" />\r\n      <meta property=\"og:locale\" content=\"en_US\" />\r\n\r\n      {/*/!* Twitter Meta Tags *!/*/}\r\n      <meta name=\"twitter:card\" content=\"summary_large_image\" />\r\n      <meta name=\"twitter:title\" content={props?.twitter_title || defaultMeta?.twitterTitle} />\r\n      <meta name=\"twitter:description\" content={props?.twitter_description || defaultMeta?.twitterDescription} />\r\n      <meta name=\"twitter:image\" content=\"https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png\" />\r\n      <meta name=\"twitter:site\" content=\"@JoinTradeReply\" />\r\n\r\n      {/* Favicon */}\r\n      <link rel=\"icon\" type=\"image/x-icon\" href={`https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`} />\r\n      <link rel=\"icon\" type=\"image/svg+xml\" href={`https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`} />\r\n      \r\n      {schemas && (\r\n        <JsonLdSchema schemas={schemas} />\r\n      )}\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAIsB;;AAHtB;;;AAEe,SAAS,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE;IAC3D,MAAM;IAEN,MAAM,cAAc;QAClB,OAAO;QACP,aAAa;QACb,WAAW;QACX,SAAS;QACT,eAAe;QACf,YAAY;QACZ,SAAS;QACT,cAAc;QACd,oBAAoB;QACpB,cAAc;IAChB;IACA,MAAM,YAAY,OAAO,YAAY;IACrC,MAAM,gBAAgB,OAAO,UAAU,CAAC,YAAY,oBAAoB,eAAe;IAEvF,qBACE;;YAEG;0BACD,6LAAC;0BAAO,OAAO,SAAS,YAAY,KAAK;;;;;;0BAOnC,6LAAC;gBAAK,MAAK;gBAAS,SAAS;;;;;;YAClC,OAAO,gBAAgB,UAAU,OAAO,YAAY,sBACnD,6LAAC;gBAAK,KAAI;gBAAY,MAAM,MAAM,cAAc;;;;;;0BAGlD,6LAAC;gBACC,MAAK;gBACL,SAAS,OAAO,eAAe,YAAY,WAAW;;;;;;YAGvD,OAAO,0BACN,6LAAC;gBAAK,KAAI;gBAAO,MAAM,OAAO;;;;;;0BAGhC,6LAAC;gBAAK,UAAS;gBAAW,SAAS,OAAO,YAAY,aAAa;;;;;;0BACnE,6LAAC;gBAAK,UAAS;gBAAiB,SAAS,OAAO,kBAAkB,aAAa;;;;;;0BAC/E,6LAAC;gBAAK,UAAS;gBAAe,SAAS,OAAO,gBAAgB,aAAa;;;;;;0BAE3E,6LAAC;gBAAK,UAAS;gBAAW,SAAQ;;;;;;0BAClC,6LAAC;gBAAK,UAAS;gBAAU,SAAQ;;;;;;0BAEjC,6LAAC;gBAAK,UAAS;gBAAiB,SAAQ;;;;;;0BACxC,6LAAC;gBAAK,UAAS;gBAAkB,SAAQ;;;;;;0BACzC,6LAAC;gBAAK,UAAS;gBAAY,SAAQ;;;;;;0BAGnC,6LAAC;gBAAK,MAAK;gBAAe,SAAQ;;;;;;0BAClC,6LAAC;gBAAK,MAAK;gBAAgB,SAAS,OAAO,iBAAiB,aAAa;;;;;;0BACzE,6LAAC;gBAAK,MAAK;gBAAsB,SAAS,OAAO,uBAAuB,aAAa;;;;;;0BACrF,6LAAC;gBAAK,MAAK;gBAAgB,SAAQ;;;;;;0BACnC,6LAAC;gBAAK,MAAK;gBAAe,SAAQ;;;;;;0BAGlC,6LAAC;gBAAK,KAAI;gBAAO,MAAK;gBAAe,MAAM,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;;;;;;0BACzH,6LAAC;gBAAK,KAAI;gBAAO,MAAK;gBAAgB,MAAM,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;;;;;;YAEzH,yBACC,6LAAC,uIAAA,CAAA,UAAY;gBAAC,SAAS;;;;;;;;AAI/B;KAtEwB", "debugId": null}}, {"offset": {"line": 5538, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5544, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/common/Account/SidebarHeading.js"], "sourcesContent": ["//import \"../../../assets/theme/_var.scss\";\r\n\r\nimport Link from \"next/link\";\r\nconst SidebarHeading = ({ title, text, Linktext, Linkicon, link = \"#\" }) => {\r\n  return (\r\n    <div className=\"sidebar_heading\">\r\n      <div className=\"sidebar_heading_top\">\r\n        <h2>{title}</h2>\r\n        <div className=\"sidebar_heading_icon\">\r\n          <Link href={link}>\r\n            <button className=\"d-flex align-items-center\">\r\n              {Linktext}\r\n              {Linkicon && <span className=\"ms-2\">{Linkicon}</span>}\r\n            </button>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n      {text && <p>{text}</p>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SidebarHeading;\r\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;;AAE3C;;;AACA,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,GAAG,EAAE;IACrE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAI;;;;;;kCACL,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM;sCACV,cAAA,6LAAC;gCAAO,WAAU;;oCACf;oCACA,0BAAY,6LAAC;wCAAK,WAAU;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAK5C,sBAAQ,6LAAC;0BAAG;;;;;;;;;;;;AAGnB;KAjBM;uCAmBS", "debugId": null}}, {"offset": {"line": 5625, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5631, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/TextInput.jsx"], "sourcesContent": ["'use client';\r\nimport { useState } from \"react\";\r\nimport { Form } from \"react-bootstrap\";\r\nimport { CloseEye, EyeIcon } from \"@/assets/svgIcons/SvgIcon\";\r\nimport \"../../css/common/textInput.scss\";\r\n\r\nconst TextInput = (props) => {\r\n  const [active, setActive] = useState(false);\r\n\r\n  /** RESTRICT USER TO ENTER e, E, +, -, . IN INPUT TYPE NUMBER */\r\n  const disabledCharacters = [\"e\", \"E\", \"+\", \"-\"];\r\n  const onKeyDown = (e) => {\r\n    if (props.disableDecimal) {\r\n      disabledCharacters.push(\".\");\r\n    }\r\n\r\n    /** RESTRICT USER TO ENTER MORE THAN MAX LENGTH IN INPUT TYPE NUMBER */\r\n    return props.type === \"number\"\r\n      ? (disabledCharacters.includes(e.key) ||\r\n        (e.key !== \"Backspace\" &&\r\n          props.maxLength &&\r\n          e.target.value.length === props.maxLength)) &&\r\n      e.preventDefault()\r\n      : props.onlyChar;\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Form.Group\r\n        className={`customInput ${props.className}`}\r\n        controlId={props.controlId}\r\n      >\r\n        {props.label ? (\r\n          <Form.Label htmlFor={props.id} className={props.classLabel}>\r\n            {props.label}\r\n            <sup>*</sup>\r\n          </Form.Label>\r\n        ) : (\r\n          \"\"\r\n        )}\r\n        <div className=\"customInput_inner\">\r\n          {props.usernameInput && (\r\n            <span className=\"spanInputCounter\">{props.value.length}/20</span>)\r\n          }\r\n          <Form.Control\r\n\r\n            disabled={props.disabled}\r\n            type={props.type === \"password\" && active ? \"text\" : props.type}\r\n            id={props.id}\r\n            name={props.name}\r\n            value={props.value}\r\n            // onKeyDown={onKeyDown}\r\n            placeholder={props.placeholder}\r\n            onBlur={props.onBlur}\r\n            onChange={props.onChange}\r\n            maxLength={props.maxLength ? props.maxLength : \"\"}\r\n            required={props.required}\r\n            min={props.min}\r\n            max={props.max}\r\n            isInvalid={props.isInvalid}\r\n            onPaste={(e) =>\r\n              props.onlyChar ? e.preventDefault() : props.onChange\r\n            }\r\n            onWheel={props.onWheel}\r\n            step={props.step}\r\n            autoComplete={props.onlyChar ? props.autoComplete : \"off\"}\r\n            pattern=\"\\S(.*\\S)?\"\r\n            title={props.title ? props.title : props?.error?.props?.children}\r\n            onInvalid={props.onInvalid}\r\n            onInput={props.onInput}\r\n            className={`${props.inputtext} ${props.isError ? \"error-field\" : \"\"}`}\r\n            readOnly={props.readOnly}\r\n            onFocus={props.onFocus}\r\n            autoFocus={props.autoFocus}\r\n          />\r\n          {props.children}\r\n          {props.type === \"password\" ? (\r\n            <span onClick={() => setActive(!active)} className=\"eyeIcon\">\r\n              {active ? <EyeIcon /> : <CloseEye />}\r\n            </span>\r\n          ) : (\r\n            \"\"\r\n          )}\r\n        </div>\r\n        {props.error}\r\n        {props.smallText ? (\r\n          <Form.Text id=\"\" muted className=\"small-text-form\">\r\n            {props.smallText}\r\n\r\n          </Form.Text>\r\n        ) : (\r\n          \"\"\r\n        )}\r\n      </Form.Group>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default TextInput;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;;AAMA,MAAM,YAAY,CAAC;;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,8DAA8D,GAC9D,MAAM,qBAAqB;QAAC;QAAK;QAAK;QAAK;KAAI;IAC/C,MAAM,YAAY,CAAC;QACjB,IAAI,MAAM,cAAc,EAAE;YACxB,mBAAmB,IAAI,CAAC;QAC1B;QAEA,qEAAqE,GACrE,OAAO,MAAM,IAAI,KAAK,WAClB,CAAC,mBAAmB,QAAQ,CAAC,EAAE,GAAG,KACjC,EAAE,GAAG,KAAK,eACT,MAAM,SAAS,IACf,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,SAAS,AAAC,KAC9C,EAAE,cAAc,KACd,MAAM,QAAQ;IACpB;IAEA,qBACE;kBACE,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;YACT,WAAW,CAAC,YAAY,EAAE,MAAM,SAAS,EAAE;YAC3C,WAAW,MAAM,SAAS;;gBAEzB,MAAM,KAAK,iBACV,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oBAAC,SAAS,MAAM,EAAE;oBAAE,WAAW,MAAM,UAAU;;wBACvD,MAAM,KAAK;sCACZ,6LAAC;sCAAI;;;;;;;;;;;2BAGP;8BAEF,6LAAC;oBAAI,WAAU;;wBACZ,MAAM,aAAa,kBAClB,6LAAC;4BAAK,WAAU;;gCAAoB,MAAM,KAAK,CAAC,MAAM;gCAAC;;;;;;;sCAEzD,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;4BAEX,UAAU,MAAM,QAAQ;4BACxB,MAAM,MAAM,IAAI,KAAK,cAAc,SAAS,SAAS,MAAM,IAAI;4BAC/D,IAAI,MAAM,EAAE;4BACZ,MAAM,MAAM,IAAI;4BAChB,OAAO,MAAM,KAAK;4BAClB,wBAAwB;4BACxB,aAAa,MAAM,WAAW;4BAC9B,QAAQ,MAAM,MAAM;4BACpB,UAAU,MAAM,QAAQ;4BACxB,WAAW,MAAM,SAAS,GAAG,MAAM,SAAS,GAAG;4BAC/C,UAAU,MAAM,QAAQ;4BACxB,KAAK,MAAM,GAAG;4BACd,KAAK,MAAM,GAAG;4BACd,WAAW,MAAM,SAAS;4BAC1B,SAAS,CAAC,IACR,MAAM,QAAQ,GAAG,EAAE,cAAc,KAAK,MAAM,QAAQ;4BAEtD,SAAS,MAAM,OAAO;4BACtB,MAAM,MAAM,IAAI;4BAChB,cAAc,MAAM,QAAQ,GAAG,MAAM,YAAY,GAAG;4BACpD,SAAQ;4BACR,OAAO,MAAM,KAAK,GAAG,MAAM,KAAK,GAAG,OAAO,OAAO,OAAO;4BACxD,WAAW,MAAM,SAAS;4BAC1B,SAAS,MAAM,OAAO;4BACtB,WAAW,GAAG,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,OAAO,GAAG,gBAAgB,IAAI;4BACrE,UAAU,MAAM,QAAQ;4BACxB,SAAS,MAAM,OAAO;4BACtB,WAAW,MAAM,SAAS;;;;;;wBAE3B,MAAM,QAAQ;wBACd,MAAM,IAAI,KAAK,2BACd,6LAAC;4BAAK,SAAS,IAAM,UAAU,CAAC;4BAAS,WAAU;sCAChD,uBAAS,6LAAC,uIAAA,CAAA,UAAO;;;;qDAAM,6LAAC,uIAAA,CAAA,WAAQ;;;;;;;;;mCAGnC;;;;;;;gBAGH,MAAM,KAAK;gBACX,MAAM,SAAS,iBACd,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;oBAAC,IAAG;oBAAG,KAAK;oBAAC,WAAU;8BAC9B,MAAM,SAAS;;;;;2BAIlB;;;;;;;;AAKV;GA1FM;KAAA;uCA4FS", "debugId": null}}, {"offset": {"line": 5780, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5786, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/StatusIndicator.js"], "sourcesContent": ["'use client';\r\nimport React from 'react';\r\nimport { CheckIcon, RedCircleCrossIcon, GreyCheckIcon } from '@/assets/svgIcons/SvgIcon';\r\n\r\n/**\r\n * StatusIndicator Component\r\n * \r\n * A reusable component for displaying save status in account detail forms\r\n * \r\n * @param {Object} props\r\n * @param {string} props.saveStatus - Current save status: 'loading', 'success', 'error', or null/undefined for default\r\n * @param {string} props.error - Error message to display when saveStatus is 'error'\r\n * @param {string} props.loadingText - Custom text for loading state (default: \"Saving...\")\r\n * @param {string} props.successText - Custom text for success state (default: \"Auto-saved\")\r\n * @param {string} props.defaultText - Custom text for default state (default: \"All changes saved\")\r\n * @param {string} props.errorText - Custom text for error state (default: uses error prop or \"Unable to save\")\r\n * @param {string} props.className - Additional CSS classes\r\n */\r\nexport default function StatusIndicator({\r\n    saveStatus,\r\n    error,\r\n    loadingText = \"Saving...\",\r\n    successText = \"Auto-saved\",\r\n    defaultText = \"All changes saved\",\r\n    errorText,\r\n    className = \"\"\r\n}) {\r\n    const getStatusContent = () => {\r\n        switch (saveStatus) {\r\n            case 'loading':\r\n                return {\r\n                    className: 'status_indicator status-loading',\r\n                    icon: (\r\n                        <img\r\n                            src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-saving.svg\"\r\n                            alt=\"Saving Icon\"\r\n                        />\r\n                    ),\r\n                    text: loadingText\r\n                };\r\n\r\n            case 'success':\r\n                return {\r\n                    className: 'status_indicator status-success',\r\n                    icon: <CheckIcon />,\r\n                    text: successText\r\n                };\r\n\r\n            case 'error':\r\n                return {\r\n                    className: 'status_indicator status-error',\r\n                    icon: <RedCircleCrossIcon />,\r\n                    text: errorText || error || 'Unable to save'\r\n                };\r\n\r\n            default:\r\n                return {\r\n                    className: 'status_indicator status-default',\r\n                    icon: <GreyCheckIcon />,\r\n                    text: defaultText\r\n                };\r\n        }\r\n    };\r\n\r\n    const { className: statusClassName, icon, text } = getStatusContent();\r\n    const finalClassName = `${statusClassName} ${className}`.trim();\r\n\r\n    return (\r\n        <div className={finalClassName}>\r\n            {icon}\r\n            <span>{text}</span>\r\n        </div>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAkBe,SAAS,gBAAgB,EACpC,UAAU,EACV,KAAK,EACL,cAAc,WAAW,EACzB,cAAc,YAAY,EAC1B,cAAc,mBAAmB,EACjC,SAAS,EACT,YAAY,EAAE,EACjB;IACG,MAAM,mBAAmB;QACrB,OAAQ;YACJ,KAAK;gBACD,OAAO;oBACH,WAAW;oBACX,oBACI,6LAAC;wBACG,KAAI;wBACJ,KAAI;;;;;;oBAGZ,MAAM;gBACV;YAEJ,KAAK;gBACD,OAAO;oBACH,WAAW;oBACX,oBAAM,6LAAC,uIAAA,CAAA,YAAS;;;;;oBAChB,MAAM;gBACV;YAEJ,KAAK;gBACD,OAAO;oBACH,WAAW;oBACX,oBAAM,6LAAC,uIAAA,CAAA,qBAAkB;;;;;oBACzB,MAAM,aAAa,SAAS;gBAChC;YAEJ;gBACI,OAAO;oBACH,WAAW;oBACX,oBAAM,6LAAC,uIAAA,CAAA,gBAAa;;;;;oBACpB,MAAM;gBACV;QACR;IACJ;IAEA,MAAM,EAAE,WAAW,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG;IACnD,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,EAAE,WAAW,CAAC,IAAI;IAE7D,qBACI,6LAAC;QAAI,WAAW;;YACX;0BACD,6LAAC;0BAAM;;;;;;;;;;;;AAGnB;KAvDwB", "debugId": null}}, {"offset": {"line": 5870, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5876, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/common/CustumDropdown.js"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport { WhiteDownArrow, SearchIcons } from '@/assets/svgIcons/SvgIcon';\r\n\r\nexport default function CustomDropdown({\r\n    options = [],\r\n    defaultValue = 'Select an option',\r\n    optionLabelKey = 'label',\r\n    onSelect,\r\n    showSearch = true,\r\n}) {\r\n    const [isOpen, setIsOpen] = useState(false);\r\n    const [selected, setSelected] = useState('');\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const dropdownRef = useRef(null);\r\n\r\n    const toggleDropdown = () => setIsOpen(!isOpen);\r\n\r\n    const handleSelect = (option) => {\r\n        const label = option[optionLabelKey];\r\n        setSelected(label);\r\n        onSelect && onSelect(option);\r\n        setIsOpen(false);\r\n        setSearchTerm('');\r\n    };\r\n\r\n    const filteredOptions = options.filter((option) =>\r\n        option[optionLabelKey].toLowerCase().includes(searchTerm.toLowerCase())\r\n    );\r\n\r\n    useEffect(() => {\r\n        const handleClickOutside = (event) => {\r\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\r\n                setIsOpen(false);\r\n            }\r\n        };\r\n\r\n        if (isOpen) {\r\n            document.addEventListener('mousedown', handleClickOutside);\r\n\r\n            setTimeout(() => {\r\n                const dropdownElement = dropdownRef.current;\r\n                if (dropdownElement) {\r\n                    const rect = dropdownElement.getBoundingClientRect();\r\n                    const dropdownBottom = rect.bottom;\r\n                    const viewportHeight = window.innerHeight;\r\n\r\n                    if (dropdownBottom > viewportHeight) {\r\n                        window.scrollBy({\r\n                            top: dropdownBottom - viewportHeight + 10,\r\n                            behavior: 'smooth',\r\n                        });\r\n                    }\r\n                }\r\n            }, 0);\r\n        }\r\n\r\n        return () => {\r\n            document.removeEventListener('mousedown', handleClickOutside);\r\n        };\r\n    }, [isOpen]);\r\n\r\n    return (\r\n        <div className=\"account-custom-select\" ref={dropdownRef}>\r\n            <div className=\"header\" onClick={toggleDropdown}>\r\n                <span>{selected || defaultValue}</span>\r\n                <WhiteDownArrow />\r\n            </div>\r\n\r\n            {isOpen && (\r\n                <div className=\"body\">\r\n                    {showSearch && (\r\n                        <div className=\"search\">\r\n                            <SearchIcons />\r\n                            <input\r\n                                type=\"text\"\r\n                                placeholder=\"Search...\"\r\n                                value={searchTerm}\r\n                                onChange={(e) => setSearchTerm(e.target.value)}\r\n                            />\r\n                        </div>\r\n                    )}\r\n\r\n                    <ul>\r\n                        {filteredOptions.length > 0 ? (\r\n                            filteredOptions.map((option, index) => (\r\n                                <li key={index} onClick={() => handleSelect(option)}>\r\n                                    {option[optionLabelKey]}\r\n                                </li>\r\n                            ))\r\n                        ) : (\r\n                            <li className=\"no-results\">No results found</li>\r\n                        )}\r\n                    </ul>\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS,eAAe,EACnC,UAAU,EAAE,EACZ,eAAe,kBAAkB,EACjC,iBAAiB,OAAO,EACxB,QAAQ,EACR,aAAa,IAAI,EACpB;;IACG,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,MAAM,iBAAiB,IAAM,UAAU,CAAC;IAExC,MAAM,eAAe,CAAC;QAClB,MAAM,QAAQ,MAAM,CAAC,eAAe;QACpC,YAAY;QACZ,YAAY,SAAS;QACrB,UAAU;QACV,cAAc;IAClB;IAEA,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAC,SACpC,MAAM,CAAC,eAAe,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAGxE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACN,MAAM;+DAAqB,CAAC;oBACxB,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAG;wBACpE,UAAU;oBACd;gBACJ;;YAEA,IAAI,QAAQ;gBACR,SAAS,gBAAgB,CAAC,aAAa;gBAEvC;gDAAW;wBACP,MAAM,kBAAkB,YAAY,OAAO;wBAC3C,IAAI,iBAAiB;4BACjB,MAAM,OAAO,gBAAgB,qBAAqB;4BAClD,MAAM,iBAAiB,KAAK,MAAM;4BAClC,MAAM,iBAAiB,OAAO,WAAW;4BAEzC,IAAI,iBAAiB,gBAAgB;gCACjC,OAAO,QAAQ,CAAC;oCACZ,KAAK,iBAAiB,iBAAiB;oCACvC,UAAU;gCACd;4BACJ;wBACJ;oBACJ;+CAAG;YACP;YAEA;4CAAO;oBACH,SAAS,mBAAmB,CAAC,aAAa;gBAC9C;;QACJ;mCAAG;QAAC;KAAO;IAEX,qBACI,6LAAC;QAAI,WAAU;QAAwB,KAAK;;0BACxC,6LAAC;gBAAI,WAAU;gBAAS,SAAS;;kCAC7B,6LAAC;kCAAM,YAAY;;;;;;kCACnB,6LAAC,uIAAA,CAAA,iBAAc;;;;;;;;;;;YAGlB,wBACG,6LAAC;gBAAI,WAAU;;oBACV,4BACG,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,uIAAA,CAAA,cAAW;;;;;0CACZ,6LAAC;gCACG,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kCAKzD,6LAAC;kCACI,gBAAgB,MAAM,GAAG,IACtB,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBACzB,6LAAC;gCAAe,SAAS,IAAM,aAAa;0CACvC,MAAM,CAAC,eAAe;+BADlB;;;;sDAKb,6LAAC;4BAAG,WAAU;sCAAa;;;;;;;;;;;;;;;;;;;;;;;AAOvD;GA9FwB;KAAA", "debugId": null}}, {"offset": {"line": 6033, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6039, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28User%29/account/details/Partial/PersonalInformation.js"], "sourcesContent": ["'use client';\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Col, Row } from 'react-bootstrap';\r\nimport { useSelector, useDispatch } from 'react-redux';\r\nimport { EditIconSvg, PlusIconSvg, WhiteDownArrow, SearchIcons } from '@/assets/svgIcons/SvgIcon';\r\nimport TextInput from '@/Components/UI/TextInput';\r\nimport StatusIndicator from '@/Components/UI/StatusIndicator';\r\nimport countries from 'world-countries';\r\nimport CustomDropdown from '@/Components/common/CustumDropdown';\r\nimport { get, put } from '@/utils/apiUtils';\r\nimport { setUser } from '@/redux/authSlice';\r\n\r\nexport default function PersonalInformation() {\r\n    const [isEditing, setIsEditing] = useState(false);\r\n    const [userData, setUserData] = useState(null);\r\n    const [loading, setLoading] = useState(true);\r\n    const [saving, setSaving] = useState(false);\r\n    const [error, setError] = useState(null);\r\n    const [saveStatus, setSaveStatus] = useState(null);\r\n\r\n    // Temporary state for editing\r\n    const [tempFirstName, setTempFirstName] = useState('');\r\n    const [tempLastName, setTempLastName] = useState('');\r\n    const [selectedCountry, setSelectedCountry] = useState('');\r\n\r\n    const dispatch = useDispatch();\r\n    const reduxUser = useSelector((state) => state?.auth?.user || null);\r\n\r\n    // Fetch user data from API\r\n    const fetchUserData = async () => {\r\n        try {\r\n            setLoading(true);\r\n            setError(null);\r\n\r\n            const controller = new AbortController();\r\n            const response = await get('/account', {}, { signal: controller.signal });\r\n\r\n            if (response.success && response.data) {\r\n                setUserData(response.data);\r\n                // Update Redux store with fresh user data\r\n                dispatch(setUser(response.data));\r\n                // Also update localStorage to ensure consistency\r\n                localStorage.setItem('user', JSON.stringify(response.data));\r\n            } else {\r\n                throw new Error(response.message || 'Failed to fetch user data');\r\n            }\r\n        } catch (err) {\r\n            console.error('Error fetching user data:', err);\r\n            setError(err.message || 'Failed to load user information');\r\n\r\n            // Fallback to Redux user data if API fails\r\n            if (reduxUser) {\r\n                setUserData(reduxUser);\r\n            }\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    // Load user data on component mount\r\n    useEffect(() => {\r\n        fetchUserData();\r\n    }, []);\r\n\r\n    // Update temp values when userData changes\r\n    useEffect(() => {\r\n        if (userData) {\r\n            setTempFirstName(userData.first_name || '');\r\n            setTempLastName(userData.last_name || '');\r\n            setSelectedCountry(userData.country || '');\r\n        }\r\n    }, [userData]);\r\n\r\n    const selectCountry = (country) => {\r\n        console.log('Selected:', country);\r\n        setSelectedCountry(country.name.common);\r\n    };\r\n\r\n    const handleEdit = () => {\r\n        if (userData) {\r\n            setTempFirstName(userData.first_name || '');\r\n            setTempLastName(userData.last_name || '');\r\n            setSelectedCountry(userData.country || '');\r\n        }\r\n        setIsEditing(true);\r\n    };\r\n\r\n    const handleSave = async () => {\r\n        if (!userData) {\r\n            setSaveStatus('error');\r\n            setError('User data not available');\r\n            setTimeout(() => setSaveStatus(null), 3000);\r\n            return;\r\n        }\r\n\r\n        try {\r\n            setSaving(true);\r\n            setSaveStatus('loading');\r\n            setError(null);\r\n\r\n            const updateData = {\r\n                first_name: tempFirstName.trim(),\r\n                last_name: tempLastName.trim(),\r\n                country: selectedCountry\r\n            };\r\n\r\n            const response = await put(`/account/update/${userData.id}`, updateData);\r\n\r\n            if (response.message) {\r\n                // Update local state with the fresh user data from response\r\n                if (response.user) {\r\n                    setUserData(response.user);\r\n                    // Update Redux store\r\n                    dispatch(setUser(response.user));\r\n                    // Update localStorage\r\n                    localStorage.setItem('user', JSON.stringify(response.user));\r\n                }\r\n\r\n                setSaveStatus('success');\r\n                setIsEditing(false);\r\n            } else {\r\n                throw new Error('Failed to update user information');\r\n            }\r\n        } catch (err) {\r\n            console.error('Error updating user data:', err);\r\n            const errorMessage = err.response?.data?.message || err.message || 'Failed to update user information';\r\n            setSaveStatus('error');\r\n            setError(errorMessage);\r\n        } finally {\r\n            setSaving(false);\r\n            setTimeout(() => setSaveStatus(null), 3000);\r\n        }\r\n    };\r\n\r\n    const handleCancel = () => {\r\n        setIsEditing(false);\r\n        if (userData) {\r\n            setTempFirstName(userData.first_name || '');\r\n            setTempLastName(userData.last_name || '');\r\n            setSelectedCountry(userData.country || '');\r\n        }\r\n        setError(null);\r\n    };\r\n\r\n    const maskName = (name) => {\r\n        if (!name || name.length === 0) return '';\r\n        if (name.length === 1) return name;\r\n        return name[0] + '*'.repeat(name.length - 1);\r\n    };\r\n\r\n    // Check if names are empty to show \"Add Name\" functionality\r\n    const hasName = () => {\r\n        return userData?.first_name || userData?.last_name;\r\n    };\r\n\r\n\r\n\r\n    // Show loading state\r\n    if (loading) {\r\n        return (\r\n            <Col lg={12} xs={12} className=\"mb-4 mb-lg-4\">\r\n                <div className=\"common_blackcard account_card\">\r\n                    <div className=\"common_blackcard_innerheader\">\r\n                        <div className=\"common_blackcard_innerheader_content\">\r\n                            <h6>Personal Information</h6>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"common_blackcard_innerbody\">\r\n                        <div className=\"account_card_list\">\r\n                            <p>Loading...</p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </Col>\r\n        );\r\n    }\r\n\r\n    // Show error state if no data and error exists\r\n    if (!userData && error) {\r\n        return (\r\n            <Col lg={12} xs={12} className=\"mb-4 mb-lg-4\">\r\n                <div className=\"common_blackcard account_card\">\r\n                    <div className=\"common_blackcard_innerheader\">\r\n                        <div className=\"common_blackcard_innerheader_content\">\r\n                            <h6>Personal Information</h6>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"common_blackcard_innerbody\">\r\n                        <div className=\"account_card_list\">\r\n                            <p style={{ color: 'red' }}>Error: {error}</p>\r\n                            <button className=\"btn-style\" onClick={fetchUserData}>\r\n                                Retry\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </Col>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <Col lg={12} xs={12} className=\"mb-4 mb-lg-4\">\r\n            <div className=\"common_blackcard account_card\">\r\n                <div className=\"common_blackcard_innerheader\">\r\n                    <div className=\"common_blackcard_innerheader_content\">\r\n                        <div className=\"account_header_main\">\r\n                            <h6>Personal Information</h6>\r\n                            <div className=\"account_status_indicator\">\r\n                                <StatusIndicator\r\n                                    saveStatus={saveStatus}\r\n                                    error={error}\r\n                                />\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"common_blackcard_innerheader_icon\">\r\n                        <button\r\n                            className=\"d-flex align-items-center\"\r\n                            onClick={handleEdit}\r\n                            disabled={saving || loading}\r\n                        >\r\n                            {hasName() ? <EditIconSvg /> : <PlusIconSvg />}\r\n                            <span className=\"ms-2\">{hasName() ? 'Update' : 'Add Name'}</span>\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                <div className=\"common_blackcard_innerbody\">\r\n                    <div className=\"account_card_list\">\r\n                        <ul>\r\n                            <li>\r\n                                <Col xs={12} md={3}>\r\n                                    <span className=\"label\">Name </span>\r\n                                </Col>\r\n                                <Col xs={12} md={9}>\r\n                                    {isEditing ? (\r\n                                        <div className=\"account_card_list_form\">\r\n                                            <Row className=\"w-100\">\r\n                                                <Col xs={6}>\r\n                                                    <TextInput\r\n                                                        className=\"mb-0\"\r\n                                                        placeholder=\"First Name\"\r\n                                                        value={tempFirstName}\r\n                                                        onChange={(e) => setTempFirstName(e.target.value)}\r\n                                                    />\r\n                                                </Col>\r\n                                                <Col xs={6}>\r\n                                                    <TextInput\r\n                                                        className=\"mb-0\"\r\n                                                        placeholder=\"Last Name\"\r\n                                                        value={tempLastName}\r\n                                                        onChange={(e) => setTempLastName(e.target.value)}\r\n                                                    />\r\n                                                </Col>\r\n                                            </Row>\r\n                                        </div>\r\n                                    ) : (\r\n                                        <span>\r\n                                            {hasName()\r\n                                                ? `${maskName(userData?.first_name)} ${maskName(userData?.last_name)}`.trim()\r\n                                                : 'Not set'\r\n                                            }\r\n                                        </span>\r\n                                    )}\r\n                                </Col>\r\n                            </li>\r\n\r\n                            <li>\r\n                                <Col xs={12} md={3}>\r\n                                    <span >Country/Region </span>\r\n                                </Col>\r\n                                <Col xs={12} md={9}>\r\n                                    {isEditing ? (\r\n                                        <div className=\"account_card_list_form\">\r\n                                            <Row>\r\n                                                <Col xs={12}>\r\n                                                    <CustomDropdown\r\n                                                        key={`country-${selectedCountry || 'none'}`}\r\n                                                        options={countries.map((c) => ({ label: c.name.common, ...c }))}\r\n                                                        defaultValue={selectedCountry || \"Select Country\"}\r\n                                                        onSelect={selectCountry}\r\n                                                    />\r\n                                                </Col>\r\n                                            </Row>\r\n                                        </div>\r\n                                    ) : (\r\n                                        <span>{userData?.country || 'Not set'}</span>\r\n                                    )}\r\n                                </Col>\r\n                            </li>\r\n                        </ul>\r\n\r\n                        {isEditing && (\r\n                            <div className=\"account_card_list_btns mt-xxl-0 mt-3\">\r\n                                <button\r\n                                    className=\"btn-style\"\r\n                                    onClick={handleSave}\r\n                                    disabled={saving}\r\n                                >\r\n                                    {saving ? 'Saving...' : 'Save'}\r\n                                </button>\r\n                                <button\r\n                                    className=\"btn-style gray-btn\"\r\n                                    onClick={handleCancel}\r\n                                    disabled={saving}\r\n                                >\r\n                                    Cancel\r\n                                </button>\r\n                            </div>\r\n                        )}\r\n\r\n                        {error && !isEditing && (\r\n                            <div className=\"mt-3\">\r\n                                <p style={{ color: 'red', fontSize: '14px' }}>\r\n                                    {error}\r\n                                </p>\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </Col>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;;AAYe,SAAS;;IACpB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,8BAA8B;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,YAAY,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,QAAU,OAAO,MAAM,QAAQ;;IAE9D,2BAA2B;IAC3B,MAAM,gBAAgB;QAClB,IAAI;YACA,WAAW;YACX,SAAS;YAET,MAAM,aAAa,IAAI;YACvB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,MAAG,AAAD,EAAE,YAAY,CAAC,GAAG;gBAAE,QAAQ,WAAW,MAAM;YAAC;YAEvE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACnC,YAAY,SAAS,IAAI;gBACzB,0CAA0C;gBAC1C,SAAS,CAAA,GAAA,4HAAA,CAAA,UAAO,AAAD,EAAE,SAAS,IAAI;gBAC9B,iDAAiD;gBACjD,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI;YAC7D,OAAO;gBACH,MAAM,IAAI,MAAM,SAAS,OAAO,IAAI;YACxC;QACJ,EAAE,OAAO,KAAK;YACV,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS,IAAI,OAAO,IAAI;YAExB,2CAA2C;YAC3C,IAAI,WAAW;gBACX,YAAY;YAChB;QACJ,SAAU;YACN,WAAW;QACf;IACJ;IAEA,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACN;QACJ;wCAAG,EAAE;IAEL,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACN,IAAI,UAAU;gBACV,iBAAiB,SAAS,UAAU,IAAI;gBACxC,gBAAgB,SAAS,SAAS,IAAI;gBACtC,mBAAmB,SAAS,OAAO,IAAI;YAC3C;QACJ;wCAAG;QAAC;KAAS;IAEb,MAAM,gBAAgB,CAAC;QACnB,QAAQ,GAAG,CAAC,aAAa;QACzB,mBAAmB,QAAQ,IAAI,CAAC,MAAM;IAC1C;IAEA,MAAM,aAAa;QACf,IAAI,UAAU;YACV,iBAAiB,SAAS,UAAU,IAAI;YACxC,gBAAgB,SAAS,SAAS,IAAI;YACtC,mBAAmB,SAAS,OAAO,IAAI;QAC3C;QACA,aAAa;IACjB;IAEA,MAAM,aAAa;QACf,IAAI,CAAC,UAAU;YACX,cAAc;YACd,SAAS;YACT,WAAW,IAAM,cAAc,OAAO;YACtC;QACJ;QAEA,IAAI;YACA,UAAU;YACV,cAAc;YACd,SAAS;YAET,MAAM,aAAa;gBACf,YAAY,cAAc,IAAI;gBAC9B,WAAW,aAAa,IAAI;gBAC5B,SAAS;YACb;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,MAAG,AAAD,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE,EAAE;YAE7D,IAAI,SAAS,OAAO,EAAE;gBAClB,4DAA4D;gBAC5D,IAAI,SAAS,IAAI,EAAE;oBACf,YAAY,SAAS,IAAI;oBACzB,qBAAqB;oBACrB,SAAS,CAAA,GAAA,4HAAA,CAAA,UAAO,AAAD,EAAE,SAAS,IAAI;oBAC9B,sBAAsB;oBACtB,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI;gBAC7D;gBAEA,cAAc;gBACd,aAAa;YACjB,OAAO;gBACH,MAAM,IAAI,MAAM;YACpB;QACJ,EAAE,OAAO,KAAK;YACV,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW,IAAI,OAAO,IAAI;YACnE,cAAc;YACd,SAAS;QACb,SAAU;YACN,UAAU;YACV,WAAW,IAAM,cAAc,OAAO;QAC1C;IACJ;IAEA,MAAM,eAAe;QACjB,aAAa;QACb,IAAI,UAAU;YACV,iBAAiB,SAAS,UAAU,IAAI;YACxC,gBAAgB,SAAS,SAAS,IAAI;YACtC,mBAAmB,SAAS,OAAO,IAAI;QAC3C;QACA,SAAS;IACb;IAEA,MAAM,WAAW,CAAC;QACd,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG,OAAO;QACvC,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;QAC9B,OAAO,IAAI,CAAC,EAAE,GAAG,IAAI,MAAM,CAAC,KAAK,MAAM,GAAG;IAC9C;IAEA,4DAA4D;IAC5D,MAAM,UAAU;QACZ,OAAO,UAAU,cAAc,UAAU;IAC7C;IAIA,qBAAqB;IACrB,IAAI,SAAS;QACT,qBACI,6LAAC,qLAAA,CAAA,MAAG;YAAC,IAAI;YAAI,IAAI;YAAI,WAAU;sBAC3B,cAAA,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;0CAAG;;;;;;;;;;;;;;;;kCAGZ,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,+CAA+C;IAC/C,IAAI,CAAC,YAAY,OAAO;QACpB,qBACI,6LAAC,qLAAA,CAAA,MAAG;YAAC,IAAI;YAAI,IAAI;YAAI,WAAU;sBAC3B,cAAA,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;0CAAG;;;;;;;;;;;;;;;;kCAGZ,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAE,OAAO;wCAAE,OAAO;oCAAM;;wCAAG;wCAAQ;;;;;;;8CACpC,6LAAC;oCAAO,WAAU;oCAAY,SAAS;8CAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ9E;IAEA,qBACI,6LAAC,qLAAA,CAAA,MAAG;QAAC,IAAI;QAAI,IAAI;QAAI,WAAU;kBAC3B,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC,6IAAA,CAAA,UAAe;4CACZ,YAAY;4CACZ,OAAO;;;;;;;;;;;;;;;;;;;;;;sCAKvB,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCACG,WAAU;gCACV,SAAS;gCACT,UAAU,UAAU;;oCAEnB,0BAAY,6LAAC,uIAAA,CAAA,cAAW;;;;6DAAM,6LAAC,uIAAA,CAAA,cAAW;;;;;kDAC3C,6LAAC;wCAAK,WAAU;kDAAQ,YAAY,WAAW;;;;;;;;;;;;;;;;;;;;;;;8BAI3D,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;;kDACG,6LAAC;;0DACG,6LAAC,qLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAI,IAAI;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAQ;;;;;;;;;;;0DAE5B,6LAAC,qLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAI,IAAI;0DACZ,0BACG,6LAAC;oDAAI,WAAU;8DACX,cAAA,6LAAC,qLAAA,CAAA,MAAG;wDAAC,WAAU;;0EACX,6LAAC,qLAAA,CAAA,MAAG;gEAAC,IAAI;0EACL,cAAA,6LAAC,wIAAA,CAAA,UAAS;oEACN,WAAU;oEACV,aAAY;oEACZ,OAAO;oEACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;0EAGxD,6LAAC,qLAAA,CAAA,MAAG;gEAAC,IAAI;0EACL,cAAA,6LAAC,wIAAA,CAAA,UAAS;oEACN,WAAU;oEACV,aAAY;oEACZ,OAAO;oEACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;yEAM/D,6LAAC;8DACI,YACK,GAAG,SAAS,UAAU,YAAY,CAAC,EAAE,SAAS,UAAU,YAAY,CAAC,IAAI,KACzE;;;;;;;;;;;;;;;;;kDAOtB,6LAAC;;0DACG,6LAAC,qLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAI,IAAI;0DACb,cAAA,6LAAC;8DAAM;;;;;;;;;;;0DAEX,6LAAC,qLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAI,IAAI;0DACZ,0BACG,6LAAC;oDAAI,WAAU;8DACX,cAAA,6LAAC,qLAAA,CAAA,MAAG;kEACA,cAAA,6LAAC,qLAAA,CAAA,MAAG;4DAAC,IAAI;sEACL,cAAA,6LAAC,gJAAA,CAAA,UAAc;gEAEX,SAAS,uHAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,IAAM,CAAC;wEAAE,OAAO,EAAE,IAAI,CAAC,MAAM;wEAAE,GAAG,CAAC;oEAAC,CAAC;gEAC7D,cAAc,mBAAmB;gEACjC,UAAU;+DAHL,CAAC,QAAQ,EAAE,mBAAmB,QAAQ;;;;;;;;;;;;;;;;;;;yEAS3D,6LAAC;8DAAM,UAAU,WAAW;;;;;;;;;;;;;;;;;;;;;;;4BAM3C,2BACG,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCACG,WAAU;wCACV,SAAS;wCACT,UAAU;kDAET,SAAS,cAAc;;;;;;kDAE5B,6LAAC;wCACG,WAAU;wCACV,SAAS;wCACT,UAAU;kDACb;;;;;;;;;;;;4BAMR,SAAS,CAAC,2BACP,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCAAE,OAAO;wCAAE,OAAO;wCAAO,UAAU;oCAAO;8CACtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrC;GAtTwB;;QAaH,4JAAA,CAAA,cAAW;QACV,4JAAA,CAAA,cAAW;;;KAdT", "debugId": null}}, {"offset": {"line": 6686, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6692, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/utils/getTimezonesForDropdown.js"], "sourcesContent": ["export function getTimezonesForDropdown() {\r\n    // Use the same timezone list as backend (DateTimeZone::listIdentifiers())\r\n    // This ensures consistency between frontend and backend\r\n    const timezones = [\r\n        'Africa/Abidjan', 'Africa/Accra', 'Africa/Addis_Ababa', 'Africa/Algiers', 'Africa/Asmara',\r\n        'Africa/Bamako', 'Africa/Bangui', 'Africa/Banjul', 'Africa/Bissau', 'Africa/Blantyre',\r\n        'Africa/Brazzaville', 'Africa/Bujumbura', 'Africa/Cairo', 'Africa/Casablanca', 'Africa/Ceuta',\r\n        'Africa/Conakry', 'Africa/Dakar', 'Africa/Dar_es_Salaam', 'Africa/Djibouti', 'Africa/Douala',\r\n        'Africa/El_Aaiun', 'Africa/Freetown', 'Africa/Gaborone', 'Africa/Harare', 'Africa/Johannesburg',\r\n        'Africa/Juba', 'Africa/Kampala', 'Africa/Khartoum', 'Africa/Kigali', 'Africa/Kinshasa',\r\n        'Africa/Lagos', 'Africa/Libreville', 'Africa/Lome', 'Africa/Luanda', 'Africa/Lubumbashi',\r\n        'Africa/Lusaka', 'Africa/Malabo', 'Africa/Maputo', 'Africa/Maseru', 'Africa/Mbabane',\r\n        'Africa/Mogadishu', 'Africa/Monrovia', 'Africa/Nairobi', 'Africa/Ndjamena', 'Africa/Niamey',\r\n        'Africa/Nouakchott', 'Africa/Ouagadougou', 'Africa/Porto-Novo', 'Africa/Sao_Tome', 'Africa/Tripoli',\r\n        'Africa/Tunis', 'Africa/Windhoek', 'America/Adak', 'America/Anchorage', 'America/Anguilla',\r\n        'America/Antigua', 'America/Araguaina', 'America/Argentina/Buenos_Aires', 'America/Argentina/Catamarca',\r\n        'America/Argentina/Cordoba', 'America/Argentina/Jujuy', 'America/Argentina/La_Rioja',\r\n        'America/Argentina/Mendoza', 'America/Argentina/Rio_Gallegos', 'America/Argentina/Salta',\r\n        'America/Argentina/San_Juan', 'America/Argentina/San_Luis', 'America/Argentina/Tucuman',\r\n        'America/Argentina/Ushuaia', 'America/Aruba', 'America/Asuncion', 'America/Atikokan',\r\n        'America/Bahia', 'America/Bahia_Banderas', 'America/Barbados', 'America/Belem', 'America/Belize',\r\n        'America/Blanc-Sablon', 'America/Boa_Vista', 'America/Bogota', 'America/Boise', 'America/Cambridge_Bay',\r\n        'America/Campo_Grande', 'America/Cancun', 'America/Caracas', 'America/Cayenne', 'America/Cayman',\r\n        'America/Chicago', 'America/Chihuahua', 'America/Costa_Rica', 'America/Creston', 'America/Cuiaba',\r\n        'America/Curacao', 'America/Danmarkshavn', 'America/Dawson', 'America/Dawson_Creek', 'America/Denver',\r\n        'America/Detroit', 'America/Dominica', 'America/Edmonton', 'America/Eirunepe', 'America/El_Salvador',\r\n        'America/Fort_Nelson', 'America/Fortaleza', 'America/Glace_Bay', 'America/Godthab', 'America/Goose_Bay',\r\n        'America/Grand_Turk', 'America/Grenada', 'America/Guadeloupe', 'America/Guatemala', 'America/Guayaquil',\r\n        'America/Guyana', 'America/Halifax', 'America/Havana', 'America/Hermosillo', 'America/Indiana/Indianapolis',\r\n        'America/Indiana/Knox', 'America/Indiana/Marengo', 'America/Indiana/Petersburg', 'America/Indiana/Tell_City',\r\n        'America/Indiana/Vevay', 'America/Indiana/Vincennes', 'America/Indiana/Winamac', 'America/Inuvik',\r\n        'America/Iqaluit', 'America/Jamaica', 'America/Juneau', 'America/Kentucky/Louisville',\r\n        'America/Kentucky/Monticello', 'America/Kralendijk', 'America/La_Paz', 'America/Lima',\r\n        'America/Los_Angeles', 'America/Lower_Princes', 'America/Maceio', 'America/Managua',\r\n        'America/Manaus', 'America/Marigot', 'America/Martinique', 'America/Matamoros', 'America/Mazatlan',\r\n        'America/Menominee', 'America/Merida', 'America/Metlakatla', 'America/Mexico_City', 'America/Miquelon',\r\n        'America/Moncton', 'America/Monterrey', 'America/Montevideo', 'America/Montserrat', 'America/Nassau',\r\n        'America/New_York', 'America/Nipigon', 'America/Nome', 'America/Noronha', 'America/North_Dakota/Beulah',\r\n        'America/North_Dakota/Center', 'America/North_Dakota/New_Salem', 'America/Ojinaga', 'America/Panama',\r\n        'America/Pangnirtung', 'America/Paramaribo', 'America/Phoenix', 'America/Port-au-Prince',\r\n        'America/Port_of_Spain', 'America/Porto_Velho', 'America/Puerto_Rico', 'America/Punta_Arenas',\r\n        'America/Rainy_River', 'America/Rankin_Inlet', 'America/Recife', 'America/Regina', 'America/Resolute',\r\n        'America/Rio_Branco', 'America/Santarem', 'America/Santiago', 'America/Santo_Domingo',\r\n        'America/Sao_Paulo', 'America/Scoresbysund', 'America/Sitka', 'America/St_Barthelemy',\r\n        'America/St_Johns', 'America/St_Kitts', 'America/St_Lucia', 'America/St_Thomas',\r\n        'America/St_Vincent', 'America/Swift_Current', 'America/Tegucigalpa', 'America/Thule',\r\n        'America/Thunder_Bay', 'America/Tijuana', 'America/Toronto', 'America/Tortola', 'America/Vancouver',\r\n        'America/Whitehorse', 'America/Winnipeg', 'America/Yakutat', 'America/Yellowknife',\r\n        'Antarctica/Casey', 'Antarctica/Davis', 'Antarctica/DumontDUrville', 'Antarctica/Macquarie',\r\n        'Antarctica/Mawson', 'Antarctica/McMurdo', 'Antarctica/Palmer', 'Antarctica/Rothera',\r\n        'Antarctica/Syowa', 'Antarctica/Troll', 'Antarctica/Vostok', 'Arctic/Longyearbyen',\r\n        'Asia/Aden', 'Asia/Almaty', 'Asia/Amman', 'Asia/Anadyr', 'Asia/Aqtau', 'Asia/Aqtobe',\r\n        'Asia/Ashgabat', 'Asia/Atyrau', 'Asia/Baghdad', 'Asia/Bahrain', 'Asia/Baku', 'Asia/Bangkok',\r\n        'Asia/Barnaul', 'Asia/Beirut', 'Asia/Bishkek', 'Asia/Brunei', 'Asia/Chita', 'Asia/Choibalsan',\r\n        'Asia/Colombo', 'Asia/Damascus', 'Asia/Dhaka', 'Asia/Dili', 'Asia/Dubai', 'Asia/Dushanbe',\r\n        'Asia/Famagusta', 'Asia/Gaza', 'Asia/Hebron', 'Asia/Ho_Chi_Minh', 'Asia/Hong_Kong',\r\n        'Asia/Hovd', 'Asia/Irkutsk', 'Asia/Jakarta', 'Asia/Jayapura', 'Asia/Jerusalem', 'Asia/Kabul',\r\n        'Asia/Kamchatka', 'Asia/Karachi', 'Asia/Kathmandu', 'Asia/Khandyga', 'Asia/Kolkata',\r\n        'Asia/Krasnoyarsk', 'Asia/Kuala_Lumpur', 'Asia/Kuching', 'Asia/Kuwait', 'Asia/Macau',\r\n        'Asia/Magadan', 'Asia/Makassar', 'Asia/Manila', 'Asia/Muscat', 'Asia/Nicosia', 'Asia/Novokuznetsk',\r\n        'Asia/Novosibirsk', 'Asia/Omsk', 'Asia/Oral', 'Asia/Phnom_Penh', 'Asia/Pontianak',\r\n        'Asia/Pyongyang', 'Asia/Qatar', 'Asia/Qostanay', 'Asia/Qyzylorda', 'Asia/Riyadh',\r\n        'Asia/Sakhalin', 'Asia/Samarkand', 'Asia/Seoul', 'Asia/Shanghai', 'Asia/Singapore',\r\n        'Asia/Srednekolymsk', 'Asia/Taipei', 'Asia/Tashkent', 'Asia/Tbilisi', 'Asia/Tehran',\r\n        'Asia/Thimphu', 'Asia/Tokyo', 'Asia/Tomsk', 'Asia/Ulaanbaatar', 'Asia/Urumqi',\r\n        'Asia/Ust-Nera', 'Asia/Vientiane', 'Asia/Vladivostok', 'Asia/Yakutsk', 'Asia/Yangon',\r\n        'Asia/Yekaterinburg', 'Asia/Yerevan', 'Atlantic/Azores', 'Atlantic/Bermuda', 'Atlantic/Canary',\r\n        'Atlantic/Cape_Verde', 'Atlantic/Faroe', 'Atlantic/Madeira', 'Atlantic/Reykjavik',\r\n        'Atlantic/South_Georgia', 'Atlantic/St_Helena', 'Atlantic/Stanley', 'Australia/Adelaide',\r\n        'Australia/Brisbane', 'Australia/Broken_Hill', 'Australia/Currie', 'Australia/Darwin',\r\n        'Australia/Eucla', 'Australia/Hobart', 'Australia/Lindeman', 'Australia/Lord_Howe',\r\n        'Australia/Melbourne', 'Australia/Perth', 'Australia/Sydney', 'Europe/Amsterdam',\r\n        'Europe/Andorra', 'Europe/Astrakhan', 'Europe/Athens', 'Europe/Belgrade', 'Europe/Berlin',\r\n        'Europe/Bratislava', 'Europe/Brussels', 'Europe/Bucharest', 'Europe/Budapest', 'Europe/Busingen',\r\n        'Europe/Chisinau', 'Europe/Copenhagen', 'Europe/Dublin', 'Europe/Gibraltar', 'Europe/Guernsey',\r\n        'Europe/Helsinki', 'Europe/Isle_of_Man', 'Europe/Istanbul', 'Europe/Jersey', 'Europe/Kaliningrad',\r\n        'Europe/Kiev', 'Europe/Kirov', 'Europe/Lisbon', 'Europe/Ljubljana', 'Europe/London',\r\n        'Europe/Luxembourg', 'Europe/Madrid', 'Europe/Malta', 'Europe/Mariehamn', 'Europe/Minsk',\r\n        'Europe/Monaco', 'Europe/Moscow', 'Europe/Oslo', 'Europe/Paris', 'Europe/Podgorica',\r\n        'Europe/Prague', 'Europe/Riga', 'Europe/Rome', 'Europe/Samara', 'Europe/San_Marino',\r\n        'Europe/Sarajevo', 'Europe/Saratov', 'Europe/Simferopol', 'Europe/Skopje', 'Europe/Sofia',\r\n        'Europe/Stockholm', 'Europe/Tallinn', 'Europe/Tirane', 'Europe/Ulyanovsk', 'Europe/Uzhgorod',\r\n        'Europe/Vaduz', 'Europe/Vatican', 'Europe/Vienna', 'Europe/Vilnius', 'Europe/Volgograd',\r\n        'Europe/Warsaw', 'Europe/Zagreb', 'Europe/Zaporozhye', 'Europe/Zurich', 'Indian/Antananarivo',\r\n        'Indian/Chagos', 'Indian/Christmas', 'Indian/Cocos', 'Indian/Comoro', 'Indian/Kerguelen',\r\n        'Indian/Mahe', 'Indian/Maldives', 'Indian/Mauritius', 'Indian/Mayotte', 'Indian/Reunion',\r\n        'Pacific/Apia', 'Pacific/Auckland', 'Pacific/Bougainville', 'Pacific/Chatham', 'Pacific/Chuuk',\r\n        'Pacific/Easter', 'Pacific/Efate', 'Pacific/Enderbury', 'Pacific/Fakaofo', 'Pacific/Fiji',\r\n        'Pacific/Funafuti', 'Pacific/Galapagos', 'Pacific/Gambier', 'Pacific/Guadalcanal',\r\n        'Pacific/Guam', 'Pacific/Honolulu', 'Pacific/Kiritimati', 'Pacific/Kosrae', 'Pacific/Kwajalein',\r\n        'Pacific/Majuro', 'Pacific/Marquesas', 'Pacific/Midway', 'Pacific/Nauru', 'Pacific/Niue',\r\n        'Pacific/Norfolk', 'Pacific/Noumea', 'Pacific/Pago_Pago', 'Pacific/Palau', 'Pacific/Pitcairn',\r\n        'Pacific/Pohnpei', 'Pacific/Port_Moresby', 'Pacific/Rarotonga', 'Pacific/Saipan',\r\n        'Pacific/Tahiti', 'Pacific/Tarawa', 'Pacific/Tongatapu', 'Pacific/Wake', 'Pacific/Wallis',\r\n        'UTC'\r\n    ];\r\n\r\n    const now = new Date();\r\n    const list = [];\r\n\r\n    for (const tz of timezones) {\r\n        try {\r\n            // Calculate offset using the same method as backend\r\n            const offsetMinutes = getTimezoneOffsetMinutes(tz, now);\r\n            const hours = Math.floor(Math.abs(offsetMinutes) / 60);\r\n            const minutes = Math.abs(offsetMinutes % 60);\r\n            const sign = offsetMinutes >= 0 ? '+' : '-';\r\n            const utc = `UTC${sign}${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;\r\n\r\n            list.push({\r\n                label: `(${utc}) ${tz.replace(/_/g, ' ')}`,\r\n                value: tz,\r\n                offsetMinutes: offsetMinutes\r\n            });\r\n        } catch {\r\n            // Skip invalid timezones\r\n        }\r\n    }\r\n\r\n    // Sort by UTC offset first (UTC−12:00 to UTC+14:00), then alphabetically by timezone name\r\n    return list.sort((a, b) => {\r\n        // First sort by offset\r\n        if (a.offsetMinutes !== b.offsetMinutes) {\r\n            return a.offsetMinutes - b.offsetMinutes;\r\n        }\r\n        // Then sort alphabetically by timezone name (not the full label)\r\n        return a.value.localeCompare(b.value);\r\n    });\r\n}\r\n\r\nfunction getTimezoneOffsetMinutes(timeZone, now = new Date()) {\r\n    try {\r\n        // Create a date formatter for the timezone\r\n        const formatter = new Intl.DateTimeFormat('en-US', {\r\n            timeZone: timeZone,\r\n            timeZoneName: 'longOffset'\r\n        });\r\n\r\n        // Get the offset string (e.g., \"GMT-05:00\")\r\n        const parts = formatter.formatToParts(now);\r\n        const offsetPart = parts.find(part => part.type === 'timeZoneName');\r\n\r\n        if (offsetPart && offsetPart.value.includes('GMT')) {\r\n            const offsetStr = offsetPart.value.replace('GMT', '');\r\n            if (offsetStr === '') return 0; // GMT+00:00\r\n\r\n            const match = offsetStr.match(/([+-])(\\d{2}):(\\d{2})/);\r\n            if (match) {\r\n                const sign = match[1] === '+' ? 1 : -1;\r\n                const hours = parseInt(match[2], 10);\r\n                const minutes = parseInt(match[3], 10);\r\n                return sign * (hours * 60 + minutes);\r\n            }\r\n        }\r\n\r\n        // Fallback method\r\n        const utcTime = new Date(now.toLocaleString('en-US', { timeZone: 'UTC' }));\r\n        const localTime = new Date(now.toLocaleString('en-US', { timeZone }));\r\n        return Math.round((localTime.getTime() - utcTime.getTime()) / 60000);\r\n    } catch {\r\n        return 0;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,SAAS;IACZ,0EAA0E;IAC1E,wDAAwD;IACxD,MAAM,YAAY;QACd;QAAkB;QAAgB;QAAsB;QAAkB;QAC1E;QAAiB;QAAiB;QAAiB;QAAiB;QACpE;QAAsB;QAAoB;QAAgB;QAAqB;QAC/E;QAAkB;QAAgB;QAAwB;QAAmB;QAC7E;QAAmB;QAAmB;QAAmB;QAAiB;QAC1E;QAAe;QAAkB;QAAmB;QAAiB;QACrE;QAAgB;QAAqB;QAAe;QAAiB;QACrE;QAAiB;QAAiB;QAAiB;QAAiB;QACpE;QAAoB;QAAmB;QAAkB;QAAmB;QAC5E;QAAqB;QAAsB;QAAqB;QAAmB;QACnF;QAAgB;QAAmB;QAAgB;QAAqB;QACxE;QAAmB;QAAqB;QAAkC;QAC1E;QAA6B;QAA2B;QACxD;QAA6B;QAAkC;QAC/D;QAA8B;QAA8B;QAC5D;QAA6B;QAAiB;QAAoB;QAClE;QAAiB;QAA0B;QAAoB;QAAiB;QAChF;QAAwB;QAAqB;QAAkB;QAAiB;QAChF;QAAwB;QAAkB;QAAmB;QAAmB;QAChF;QAAmB;QAAqB;QAAsB;QAAmB;QACjF;QAAmB;QAAwB;QAAkB;QAAwB;QACrF;QAAmB;QAAoB;QAAoB;QAAoB;QAC/E;QAAuB;QAAqB;QAAqB;QAAmB;QACpF;QAAsB;QAAmB;QAAsB;QAAqB;QACpF;QAAkB;QAAmB;QAAkB;QAAsB;QAC7E;QAAwB;QAA2B;QAA8B;QACjF;QAAyB;QAA6B;QAA2B;QACjF;QAAmB;QAAmB;QAAkB;QACxD;QAA+B;QAAsB;QAAkB;QACvE;QAAuB;QAAyB;QAAkB;QAClE;QAAkB;QAAmB;QAAsB;QAAqB;QAChF;QAAqB;QAAkB;QAAsB;QAAuB;QACpF;QAAmB;QAAqB;QAAsB;QAAsB;QACpF;QAAoB;QAAmB;QAAgB;QAAmB;QAC1E;QAA+B;QAAkC;QAAmB;QACpF;QAAuB;QAAsB;QAAmB;QAChE;QAAyB;QAAuB;QAAuB;QACvE;QAAuB;QAAwB;QAAkB;QAAkB;QACnF;QAAsB;QAAoB;QAAoB;QAC9D;QAAqB;QAAwB;QAAiB;QAC9D;QAAoB;QAAoB;QAAoB;QAC5D;QAAsB;QAAyB;QAAuB;QACtE;QAAuB;QAAmB;QAAmB;QAAmB;QAChF;QAAsB;QAAoB;QAAmB;QAC7D;QAAoB;QAAoB;QAA6B;QACrE;QAAqB;QAAsB;QAAqB;QAChE;QAAoB;QAAoB;QAAqB;QAC7D;QAAa;QAAe;QAAc;QAAe;QAAc;QACvE;QAAiB;QAAe;QAAgB;QAAgB;QAAa;QAC7E;QAAgB;QAAe;QAAgB;QAAe;QAAc;QAC5E;QAAgB;QAAiB;QAAc;QAAa;QAAc;QAC1E;QAAkB;QAAa;QAAe;QAAoB;QAClE;QAAa;QAAgB;QAAgB;QAAiB;QAAkB;QAChF;QAAkB;QAAgB;QAAkB;QAAiB;QACrE;QAAoB;QAAqB;QAAgB;QAAe;QACxE;QAAgB;QAAiB;QAAe;QAAe;QAAgB;QAC/E;QAAoB;QAAa;QAAa;QAAmB;QACjE;QAAkB;QAAc;QAAiB;QAAkB;QACnE;QAAiB;QAAkB;QAAc;QAAiB;QAClE;QAAsB;QAAe;QAAiB;QAAgB;QACtE;QAAgB;QAAc;QAAc;QAAoB;QAChE;QAAiB;QAAkB;QAAoB;QAAgB;QACvE;QAAsB;QAAgB;QAAmB;QAAoB;QAC7E;QAAuB;QAAkB;QAAoB;QAC7D;QAA0B;QAAsB;QAAoB;QACpE;QAAsB;QAAyB;QAAoB;QACnE;QAAmB;QAAoB;QAAsB;QAC7D;QAAuB;QAAmB;QAAoB;QAC9D;QAAkB;QAAoB;QAAiB;QAAmB;QAC1E;QAAqB;QAAmB;QAAoB;QAAmB;QAC/E;QAAmB;QAAqB;QAAiB;QAAoB;QAC7E;QAAmB;QAAsB;QAAmB;QAAiB;QAC7E;QAAe;QAAgB;QAAiB;QAAoB;QACpE;QAAqB;QAAiB;QAAgB;QAAoB;QAC1E;QAAiB;QAAiB;QAAe;QAAgB;QACjE;QAAiB;QAAe;QAAe;QAAiB;QAChE;QAAmB;QAAkB;QAAqB;QAAiB;QAC3E;QAAoB;QAAkB;QAAiB;QAAoB;QAC3E;QAAgB;QAAkB;QAAiB;QAAkB;QACrE;QAAiB;QAAiB;QAAqB;QAAiB;QACxE;QAAiB;QAAoB;QAAgB;QAAiB;QACtE;QAAe;QAAmB;QAAoB;QAAkB;QACxE;QAAgB;QAAoB;QAAwB;QAAmB;QAC/E;QAAkB;QAAiB;QAAqB;QAAmB;QAC3E;QAAoB;QAAqB;QAAmB;QAC5D;QAAgB;QAAoB;QAAsB;QAAkB;QAC5E;QAAkB;QAAqB;QAAkB;QAAiB;QAC1E;QAAmB;QAAkB;QAAqB;QAAiB;QAC3E;QAAmB;QAAwB;QAAqB;QAChE;QAAkB;QAAkB;QAAqB;QAAgB;QACzE;KACH;IAED,MAAM,MAAM,IAAI;IAChB,MAAM,OAAO,EAAE;IAEf,KAAK,MAAM,MAAM,UAAW;QACxB,IAAI;YACA,oDAAoD;YACpD,MAAM,gBAAgB,yBAAyB,IAAI;YACnD,MAAM,QAAQ,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,iBAAiB;YACnD,MAAM,UAAU,KAAK,GAAG,CAAC,gBAAgB;YACzC,MAAM,OAAO,iBAAiB,IAAI,MAAM;YACxC,MAAM,MAAM,CAAC,GAAG,EAAE,OAAO,OAAO,OAAO,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,SAAS,QAAQ,CAAC,GAAG,MAAM;YAE7F,KAAK,IAAI,CAAC;gBACN,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,GAAG,OAAO,CAAC,MAAM,MAAM;gBAC1C,OAAO;gBACP,eAAe;YACnB;QACJ,EAAE,OAAM;QACJ,yBAAyB;QAC7B;IACJ;IAEA,0FAA0F;IAC1F,OAAO,KAAK,IAAI,CAAC,CAAC,GAAG;QACjB,uBAAuB;QACvB,IAAI,EAAE,aAAa,KAAK,EAAE,aAAa,EAAE;YACrC,OAAO,EAAE,aAAa,GAAG,EAAE,aAAa;QAC5C;QACA,iEAAiE;QACjE,OAAO,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE,KAAK;IACxC;AACJ;AAEA,SAAS,yBAAyB,QAAQ,EAAE,MAAM,IAAI,MAAM;IACxD,IAAI;QACA,2CAA2C;QAC3C,MAAM,YAAY,IAAI,KAAK,cAAc,CAAC,SAAS;YAC/C,UAAU;YACV,cAAc;QAClB;QAEA,4CAA4C;QAC5C,MAAM,QAAQ,UAAU,aAAa,CAAC;QACtC,MAAM,aAAa,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;QAEpD,IAAI,cAAc,WAAW,KAAK,CAAC,QAAQ,CAAC,QAAQ;YAChD,MAAM,YAAY,WAAW,KAAK,CAAC,OAAO,CAAC,OAAO;YAClD,IAAI,cAAc,IAAI,OAAO,GAAG,YAAY;YAE5C,MAAM,QAAQ,UAAU,KAAK,CAAC;YAC9B,IAAI,OAAO;gBACP,MAAM,OAAO,KAAK,CAAC,EAAE,KAAK,MAAM,IAAI,CAAC;gBACrC,MAAM,QAAQ,SAAS,KAAK,CAAC,EAAE,EAAE;gBACjC,MAAM,UAAU,SAAS,KAAK,CAAC,EAAE,EAAE;gBACnC,OAAO,OAAO,CAAC,QAAQ,KAAK,OAAO;YACvC;QACJ;QAEA,kBAAkB;QAClB,MAAM,UAAU,IAAI,KAAK,IAAI,cAAc,CAAC,SAAS;YAAE,UAAU;QAAM;QACvE,MAAM,YAAY,IAAI,KAAK,IAAI,cAAc,CAAC,SAAS;YAAE;QAAS;QAClE,OAAO,KAAK,KAAK,CAAC,CAAC,UAAU,OAAO,KAAK,QAAQ,OAAO,EAAE,IAAI;IAClE,EAAE,OAAM;QACJ,OAAO;IACX;AACJ", "debugId": null}}, {"offset": {"line": 7191, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7197, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/CommonTooltip.js"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef, useEffect, useCallback } from \"react\";\r\nimport { createPortal } from \"react-dom\";\r\nimport \"../../css/common/CommonTooltip.scss\";\r\n\r\nconst CommonTooltip = ({\r\n  children,\r\n  content,\r\n  position = \"top-right\",\r\n  className = \"\"\r\n}) => {\r\n  const [visible, setVisible] = useState(false);\r\n  const [tooltipStyle, setTooltipStyle] = useState({});\r\n  const wrapperRef = useRef(null);\r\n  const tooltipRef = useRef(null);\r\n\r\n  const updateTooltipPosition = useCallback(() => {\r\n    if (!wrapperRef.current || !tooltipRef.current) return;\r\n\r\n    const triggerRect = wrapperRef.current.getBoundingClientRect();\r\n    const tooltipRect = tooltipRef.current.getBoundingClientRect();\r\n\r\n    const scrollY = window.scrollY || window.pageYOffset;\r\n    const scrollX = window.scrollX || window.pageXOffset;\r\n\r\n    let top = triggerRect.top + scrollY - tooltipRect.height - 8;\r\n    let left = triggerRect.left + scrollX;\r\n\r\n    switch (position) {\r\n      case \"top-right\":\r\n        left += triggerRect.width - tooltipRect.width;\r\n        break;\r\n      case \"bottom-left\":\r\n        top = triggerRect.bottom + scrollY + 8;\r\n        break;\r\n      case \"bottom-right\":\r\n        top = triggerRect.bottom + scrollY + 8;\r\n        left += triggerRect.width - tooltipRect.width;\r\n        break;\r\n      case \"center-top\":\r\n        left += triggerRect.width / 2 - tooltipRect.width / 2;\r\n        break;\r\n      case \"center-bottom\":\r\n        top = triggerRect.bottom + scrollY + 8;\r\n        left += triggerRect.width / 2 - tooltipRect.width / 2;\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n\r\n    setTooltipStyle({\r\n      position: \"absolute\",\r\n      top: `${top}px`,\r\n      left: `${left}px`,\r\n      zIndex: 9999,\r\n    });\r\n  }, [position]);\r\n\r\n  useEffect(() => {\r\n    if (visible) {\r\n      updateTooltipPosition();\r\n\r\n      window.addEventListener(\"scroll\", updateTooltipPosition, true); // true captures bubbling scroll\r\n      window.addEventListener(\"resize\", updateTooltipPosition);\r\n\r\n      return () => {\r\n        window.removeEventListener(\"scroll\", updateTooltipPosition, true);\r\n        window.removeEventListener(\"resize\", updateTooltipPosition);\r\n      };\r\n    }\r\n  }, [visible, updateTooltipPosition]);\r\n\r\n  return (\r\n    <div\r\n      className={`tooltip-wrapper ${className}`}\r\n      onMouseEnter={() => setVisible(true)}\r\n      onMouseLeave={() => setVisible(false)}\r\n      onFocus={() => setVisible(true)}\r\n      onBlur={() => setVisible(false)}\r\n      ref={wrapperRef}\r\n      style={{ display: \"inline-block\" }}\r\n    >\r\n      {children}\r\n      {visible &&\r\n        createPortal(\r\n          <div\r\n            ref={tooltipRef}\r\n            className=\"tooltip-box\"\r\n            style={tooltipStyle}\r\n          >\r\n            {content}\r\n          </div>,\r\n          document.body\r\n        )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CommonTooltip;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;;AAMA,MAAM,gBAAgB,CAAC,EACrB,QAAQ,EACR,OAAO,EACP,WAAW,WAAW,EACtB,YAAY,EAAE,EACf;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAClD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACxC,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,WAAW,OAAO,EAAE;YAEhD,MAAM,cAAc,WAAW,OAAO,CAAC,qBAAqB;YAC5D,MAAM,cAAc,WAAW,OAAO,CAAC,qBAAqB;YAE5D,MAAM,UAAU,OAAO,OAAO,IAAI,OAAO,WAAW;YACpD,MAAM,UAAU,OAAO,OAAO,IAAI,OAAO,WAAW;YAEpD,IAAI,MAAM,YAAY,GAAG,GAAG,UAAU,YAAY,MAAM,GAAG;YAC3D,IAAI,OAAO,YAAY,IAAI,GAAG;YAE9B,OAAQ;gBACN,KAAK;oBACH,QAAQ,YAAY,KAAK,GAAG,YAAY,KAAK;oBAC7C;gBACF,KAAK;oBACH,MAAM,YAAY,MAAM,GAAG,UAAU;oBACrC;gBACF,KAAK;oBACH,MAAM,YAAY,MAAM,GAAG,UAAU;oBACrC,QAAQ,YAAY,KAAK,GAAG,YAAY,KAAK;oBAC7C;gBACF,KAAK;oBACH,QAAQ,YAAY,KAAK,GAAG,IAAI,YAAY,KAAK,GAAG;oBACpD;gBACF,KAAK;oBACH,MAAM,YAAY,MAAM,GAAG,UAAU;oBACrC,QAAQ,YAAY,KAAK,GAAG,IAAI,YAAY,KAAK,GAAG;oBACpD;gBACF;oBACE;YACJ;YAEA,gBAAgB;gBACd,UAAU;gBACV,KAAK,GAAG,IAAI,EAAE,CAAC;gBACf,MAAM,GAAG,KAAK,EAAE,CAAC;gBACjB,QAAQ;YACV;QACF;2DAAG;QAAC;KAAS;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,SAAS;gBACX;gBAEA,OAAO,gBAAgB,CAAC,UAAU,uBAAuB,OAAO,gCAAgC;gBAChG,OAAO,gBAAgB,CAAC,UAAU;gBAElC;+CAAO;wBACL,OAAO,mBAAmB,CAAC,UAAU,uBAAuB;wBAC5D,OAAO,mBAAmB,CAAC,UAAU;oBACvC;;YACF;QACF;kCAAG;QAAC;QAAS;KAAsB;IAEnC,qBACE,6LAAC;QACC,WAAW,CAAC,gBAAgB,EAAE,WAAW;QACzC,cAAc,IAAM,WAAW;QAC/B,cAAc,IAAM,WAAW;QAC/B,SAAS,IAAM,WAAW;QAC1B,QAAQ,IAAM,WAAW;QACzB,KAAK;QACL,OAAO;YAAE,SAAS;QAAe;;YAEhC;YACA,yBACC,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,gBACT,6LAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;0BAEN;;;;;sBAEH,SAAS,IAAI;;;;;;;AAIvB;GA3FM;KAAA;uCA6FS", "debugId": null}}, {"offset": {"line": 7310, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7316, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28User%29/account/details/Partial/LocalizationSettings.js"], "sourcesContent": ["'use client';\r\nimport React, { useMemo, useState, useEffect } from 'react';\r\nimport { Col, Row } from 'react-bootstrap';\r\nimport { useSelector, useDispatch } from 'react-redux';\r\nimport { EditIconSvg, SolidInfoIcon } from '@/assets/svgIcons/SvgIcon';\r\nimport CustomDropdown from '@/Components/common/CustumDropdown';\r\nimport StatusIndicator from '@/Components/UI/StatusIndicator';\r\nimport { getTimezonesForDropdown } from '@/utils/getTimezonesForDropdown';\r\nimport CommonTooltip from '@/Components/UI/CommonTooltip';\r\nimport { get, put } from '@/utils/apiUtils';\r\nimport { setUser } from '@/redux/authSlice';\r\n\r\nexport default function LocalizationSettings() {\r\n    // Use the same timezone options as portfolio manager for consistency\r\n    const timezoneOptions = useMemo(() => {\r\n        return getTimezonesForDropdown();\r\n    }, []);\r\n\r\n    // Updated to match backend configuration\r\n    const languages = [\r\n        { label: 'English', value: 'en' },\r\n        { label: 'French', value: 'fr' },\r\n        { label: 'German', value: 'de' },\r\n        { label: 'Spanish', value: 'es' },\r\n        { label: 'Italian', value: 'it' },\r\n        { label: 'Portuguese', value: 'pt' },\r\n        { label: 'Russian', value: 'ru' },\r\n        { label: 'Arabic', value: 'ar' },\r\n        { label: 'Chinese', value: 'zh' },\r\n        { label: 'Japanese', value: 'ja' },\r\n        { label: 'Korean', value: 'ko' },\r\n        { label: 'Urdu', value: 'ur' },\r\n    ];\r\n\r\n    // Updated to match backend configuration\r\n    const currencies = [\r\n        { label: 'USD', value: 'USD' },\r\n        { label: 'EUR', value: 'EUR' },\r\n        { label: 'GBP', value: 'GBP' },\r\n        { label: 'PKR', value: 'PKR' },\r\n        { label: 'AUD', value: 'AUD' },\r\n        { label: 'CAD', value: 'CAD' },\r\n        { label: 'JPY', value: 'JPY' },\r\n        { label: 'CNY', value: 'CNY' },\r\n        { label: 'INR', value: 'INR' },\r\n        { label: 'CHF', value: 'CHF' },\r\n        { label: 'AED', value: 'AED' },\r\n        { label: 'SAR', value: 'SAR' },\r\n        { label: 'KWD', value: 'KWD' },\r\n        { label: 'BHD', value: 'BHD' },\r\n        { label: 'QAR', value: 'QAR' },\r\n        { label: 'MYR', value: 'MYR' },\r\n        { label: 'SGD', value: 'SGD' },\r\n        { label: 'THB', value: 'THB' },\r\n        { label: 'HKD', value: 'HKD' },\r\n    ];\r\n\r\n    // Updated to match backend configuration\r\n    const numberFormatOptions = [\r\n        { label: 'Dot Separator (1,234.56)', value: '1,234.56' },\r\n        { label: 'Comma Separator (1.234,56)', value: '1.234,56' },\r\n        { label: 'No Separator (1234.56)', value: '1234.56' },\r\n    ];\r\n\r\n    const [isEditing, setIsEditing] = useState(false);\r\n    const [userData, setUserData] = useState(null);\r\n    const [loading, setLoading] = useState(true);\r\n    const [saving, setSaving] = useState(false);\r\n    const [error, setError] = useState(null);\r\n    const [saveStatus, setSaveStatus] = useState(null);\r\n\r\n    // Temporary state for editing\r\n    const [tempLanguage, setTempLanguage] = useState('');\r\n    const [tempTimezone, setTempTimezone] = useState('');\r\n    const [tempCurrency, setTempCurrency] = useState('');\r\n    const [tempNumberFormat, setTempNumberFormat] = useState('');\r\n\r\n    const dispatch = useDispatch();\r\n    const reduxUser = useSelector((state) => state?.auth?.user || null);\r\n\r\n    // Fetch user data from API\r\n    const fetchUserData = async () => {\r\n        try {\r\n            setLoading(true);\r\n            setError(null);\r\n\r\n            const controller = new AbortController();\r\n            const response = await get('/account', {}, { signal: controller.signal });\r\n\r\n            if (response.success && response.data) {\r\n                setUserData(response.data);\r\n                // Update Redux store with fresh user data\r\n                dispatch(setUser(response.data));\r\n                // Also update localStorage to ensure consistency\r\n                localStorage.setItem('user', JSON.stringify(response.data));\r\n            } else {\r\n                throw new Error(response.message || 'Failed to fetch user data');\r\n            }\r\n        } catch (err) {\r\n            console.error('Error fetching user data:', err);\r\n            setError(err.message || 'Failed to load user information');\r\n\r\n            // Fallback to Redux user data if API fails\r\n            if (reduxUser) {\r\n                setUserData(reduxUser);\r\n            }\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    // Load user data on component mount\r\n    useEffect(() => {\r\n        fetchUserData();\r\n    }, []);\r\n\r\n    // Update temp values when userData changes\r\n    useEffect(() => {\r\n        if (userData) {\r\n            setTempLanguage(userData.language || '');\r\n            setTempTimezone(userData.timezone || '');\r\n            setTempCurrency(userData.currency || '');\r\n            setTempNumberFormat(userData.number_format || '');\r\n        }\r\n    }, [userData]);\r\n\r\n    // Helper functions to get display labels\r\n    const getLanguageLabel = (value) => {\r\n        const lang = languages.find(l => l.value === value);\r\n        return lang ? lang.label : value || 'Not set';\r\n    };\r\n\r\n    const getTimezoneLabel = (value) => {\r\n        const tz = timezoneOptions.find(t => t.value === value);\r\n        return tz ? tz.label : value || 'Not set';\r\n    };\r\n\r\n    const getCurrencyLabel = (value) => {\r\n        const curr = currencies.find(c => c.value === value);\r\n        return curr ? curr.label : value || 'Not set';\r\n    };\r\n\r\n    const getNumberFormatLabel = (value) => {\r\n        const format = numberFormatOptions.find(f => f.value === value);\r\n        return format ? format.label : value || 'Not set';\r\n    };\r\n\r\n    const handleEditClick = () => {\r\n        if (userData) {\r\n            setTempLanguage(userData.language || '');\r\n            setTempTimezone(userData.timezone || '');\r\n            setTempCurrency(userData.currency || '');\r\n            setTempNumberFormat(userData.number_format || '');\r\n        }\r\n        setIsEditing(true);\r\n    };\r\n\r\n    const handleSave = async () => {\r\n        if (!userData) {\r\n            setSaveStatus('error');\r\n            setError('User data not available');\r\n            setTimeout(() => setSaveStatus(null), 3000);\r\n            return;\r\n        }\r\n\r\n        try {\r\n            setSaving(true);\r\n            setSaveStatus('loading');\r\n            setError(null);\r\n\r\n            const updateData = {\r\n                language: tempLanguage || null,\r\n                timezone: tempTimezone || null,\r\n                currency: tempCurrency || null,\r\n                number_format: tempNumberFormat || null\r\n            };\r\n\r\n            const response = await put(`/account/update/${userData.id}`, updateData);\r\n\r\n            if (response.message) {\r\n                // Update local state with the fresh user data from response\r\n                if (response.user) {\r\n                    setUserData(response.user);\r\n                    // Update Redux store\r\n                    dispatch(setUser(response.user));\r\n                    // Update localStorage\r\n                    localStorage.setItem('user', JSON.stringify(response.user));\r\n                }\r\n\r\n                setSaveStatus('success');\r\n                setIsEditing(false);\r\n            } else {\r\n                throw new Error('Failed to update localization settings');\r\n            }\r\n        } catch (err) {\r\n            console.error('Error updating localization settings:', err);\r\n            const errorMessage = err.response?.data?.message || err.message || 'Failed to update localization settings';\r\n            setSaveStatus('error');\r\n            setError(errorMessage);\r\n        } finally {\r\n            setSaving(false);\r\n            setTimeout(() => setSaveStatus(null), 3000);\r\n        }\r\n    };\r\n\r\n    const handleCancel = () => {\r\n        setIsEditing(false);\r\n        if (userData) {\r\n            setTempLanguage(userData.language || '');\r\n            setTempTimezone(userData.timezone || '');\r\n            setTempCurrency(userData.currency || '');\r\n            setTempNumberFormat(userData.number_format || '');\r\n        }\r\n        setError(null);\r\n    };\r\n\r\n\r\n\r\n    // Show loading state\r\n    if (loading) {\r\n        return (\r\n            <Col lg={12} xs={12} className=\"mb-3 mb-lg-4\">\r\n                <div className=\"common_blackcard account_card\">\r\n                    <div className=\"common_blackcard_innerheader\">\r\n                        <div className=\"common_blackcard_innerheader_content\">\r\n                            <h6>Localization Settings</h6>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"common_blackcard_innerbody\">\r\n                        <div className=\"account_card_list\">\r\n                            <p>Loading...</p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </Col>\r\n        );\r\n    }\r\n\r\n    // Show error state if no data and error exists\r\n    if (!userData && error) {\r\n        return (\r\n            <Col lg={12} xs={12} className=\"mb-3 mb-lg-4\">\r\n                <div className=\"common_blackcard account_card\">\r\n                    <div className=\"common_blackcard_innerheader\">\r\n                        <div className=\"common_blackcard_innerheader_content\">\r\n                            <h6>Localization Settings</h6>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"common_blackcard_innerbody\">\r\n                        <div className=\"account_card_list\">\r\n                            <p style={{ color: 'red' }}>Error: {error}</p>\r\n                            <button className=\"btn-style\" onClick={fetchUserData}>\r\n                                Retry\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </Col>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <Col lg={12} xs={12} className=\"mb-3 mb-lg-4\">\r\n            <div className=\"common_blackcard account_card\">\r\n                <div className=\"common_blackcard_innerheader\">\r\n                    <div className=\"common_blackcard_innerheader_content\">\r\n                        <div className=\"account_header_main\">\r\n                            <h6>Localization Settings</h6>\r\n                            <div className=\"account_status_indicator\">\r\n                                <StatusIndicator\r\n                                    saveStatus={saveStatus}\r\n                                    error={error}\r\n                                />\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"common_blackcard_innerheader_icon\">\r\n                        {!isEditing && (\r\n                            <button\r\n                                className=\"d-flex align-items-center\"\r\n                                onClick={handleEditClick}\r\n                                disabled={saving || loading}\r\n                            >\r\n                                <EditIconSvg />\r\n                                <span className=\"ms-2\">Update</span>\r\n                            </button>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n                <div className=\"common_blackcard_innerbody\">\r\n                    <div className=\"account_card_list\">\r\n                        <ul>\r\n                            {/* Language */}\r\n                            <li>\r\n                                <Col xs={12} md={3}>\r\n                                    <span>Language</span>\r\n                                </Col>\r\n                                <Col xs={12} md={9}>\r\n                                    {isEditing ? (\r\n                                        <div className=\"account_card_list_form\">\r\n                                            <Row>\r\n                                                <Col xs={12}>\r\n                                                    <CustomDropdown\r\n                                                        key={`language-${tempLanguage || 'none'}`}\r\n                                                        options={languages}\r\n                                                        defaultValue={getLanguageLabel(tempLanguage)}\r\n                                                        onSelect={(val) => setTempLanguage(val.value)}\r\n                                                    />\r\n                                                </Col>\r\n                                            </Row>\r\n                                        </div>\r\n                                    ) : (\r\n                                        <span>{getLanguageLabel(userData?.language)}</span>\r\n                                    )}\r\n                                </Col>\r\n                            </li>\r\n                            {/* Timezone */}\r\n                            <li>\r\n                                <Col xs={12} md={3}>\r\n                                    <span>Timezone</span>\r\n                                </Col>\r\n                                <Col xs={12} md={9}>\r\n                                    {isEditing ? (\r\n                                        <div className=\"account_card_list_form\">\r\n                                            <Row>\r\n                                                <Col xs={12}>\r\n                                                    <CustomDropdown\r\n                                                        key={`timezone-${tempTimezone || 'none'}`}\r\n                                                        options={timezoneOptions}\r\n                                                        defaultValue={getTimezoneLabel(tempTimezone)}\r\n                                                        onSelect={(val) => setTempTimezone(val.value)}\r\n                                                    />\r\n                                                </Col>\r\n                                            </Row>\r\n                                        </div>\r\n                                    ) : (\r\n                                        <span>{getTimezoneLabel(userData?.timezone)}</span>\r\n                                    )}\r\n                                </Col>\r\n                            </li>\r\n                            {/* Currency */}\r\n                            <li>\r\n                                <Col xs={12} md={3}>\r\n                                    <span>Currency</span>\r\n                                </Col>\r\n                                <Col xs={12} md={9}>\r\n                                    {isEditing ? (\r\n                                        <div className=\"account_card_list_form\">\r\n                                            <Row>\r\n                                                <Col xs={12}>\r\n                                                    <CustomDropdown\r\n                                                        key={`currency-${tempCurrency || 'none'}`}\r\n                                                        options={currencies}\r\n                                                        defaultValue={getCurrencyLabel(tempCurrency)}\r\n                                                        onSelect={(val) => setTempCurrency(val.value)}\r\n                                                    />\r\n                                                </Col>\r\n                                            </Row>\r\n                                        </div>\r\n                                    ) : (\r\n                                        <span>{getCurrencyLabel(userData?.currency)}</span>\r\n                                    )}\r\n                                </Col>\r\n                            </li>\r\n                            {/* Number Format Display */}\r\n                            <li>\r\n                                <Col xs={12} md={3}>\r\n                                    <div className='d-flex align-items-center gap-1'>\r\n                                        <span className='pe-0'>Number Format Display</span>\r\n                                        <CommonTooltip\r\n                                            className=\"CustomTooltip\"\r\n                                            content={\r\n                                                <>\r\n                                                    <p className='mb-2'>\r\n                                                        Choose how numbers—such as prices, metrics, and marketplace values—are displayed across your account. This setting controls <span className='fw-800 width-autofit pe-0'>visual formatting only</span>, not how numbers are calculated or stored.\r\n                                                    </p>\r\n                                                    <p className='mb-1'>• <span className='fw-800 width-autofit pe-0'>Dot Separator:</span> uses commas for thousands and a dot for decimals</p>\r\n                                                    <p className='mb-2'>• <span className='fw-800 width-autofit pe-0'>Comma Separator:</span> uses dots for thousands and a comma for decimals</p>\r\n                                                    <p className='mb-2'>\r\n                                                        This setting applies globally to all account-level and marketplace pages. Individual Trade Accounts may define their own display preference, but it only affects how numbers appear <span className='fw-800 width-autofit pe-0'>within the dashboard experience.</span>\r\n                                                    </p>\r\n                                                    <p className='mb-2'>\r\n                                                        When entering numbers, use only digits and the correct decimal separator. Thousands separators are added automatically and cannot be typed manually.\r\n                                                    </p>\r\n                                                </>\r\n                                            }\r\n                                            position=\"top-right\"\r\n                                        >\r\n                                            <SolidInfoIcon />\r\n                                        </CommonTooltip>\r\n                                    </div>\r\n                                </Col>\r\n                                <Col xs={12} md={9}>\r\n                                    {isEditing ? (\r\n                                        <div className=\"account_card_list_form\">\r\n                                            <Row>\r\n                                                <Col xs={12}>\r\n                                                    <CustomDropdown\r\n                                                        key={`number-format-${tempNumberFormat || 'none'}`}\r\n                                                        options={numberFormatOptions}\r\n                                                        defaultValue={getNumberFormatLabel(tempNumberFormat)}\r\n                                                        onSelect={(val) => setTempNumberFormat(val.value)}\r\n                                                    />\r\n                                                </Col>\r\n                                            </Row>\r\n                                        </div>\r\n                                    ) : (\r\n                                        <span>{getNumberFormatLabel(userData?.number_format)}</span>\r\n                                    )}\r\n                                </Col>\r\n                            </li>\r\n                        </ul>\r\n\r\n                        {isEditing && (\r\n                            <div className=\"account_card_list_btns\">\r\n                                <button\r\n                                    className=\"btn-style\"\r\n                                    onClick={handleSave}\r\n                                    disabled={saving}\r\n                                >\r\n                                    {saving ? 'Saving...' : 'Save'}\r\n                                </button>\r\n                                <button\r\n                                    className=\"btn-style gray-btn\"\r\n                                    onClick={handleCancel}\r\n                                    disabled={saving}\r\n                                >\r\n                                    Cancel\r\n                                </button>\r\n                            </div>\r\n                        )}\r\n\r\n                        {error && !isEditing && (\r\n                            <div className=\"mt-3\">\r\n                                <p style={{ color: 'red', fontSize: '14px' }}>\r\n                                    {error}\r\n                                </p>\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </Col >\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;;AAYe,SAAS;;IACpB,qEAAqE;IACrE,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yDAAE;YAC5B,OAAO,CAAA,GAAA,0IAAA,CAAA,0BAAuB,AAAD;QACjC;wDAAG,EAAE;IAEL,yCAAyC;IACzC,MAAM,YAAY;QACd;YAAE,OAAO;YAAW,OAAO;QAAK;QAChC;YAAE,OAAO;YAAU,OAAO;QAAK;QAC/B;YAAE,OAAO;YAAU,OAAO;QAAK;QAC/B;YAAE,OAAO;YAAW,OAAO;QAAK;QAChC;YAAE,OAAO;YAAW,OAAO;QAAK;QAChC;YAAE,OAAO;YAAc,OAAO;QAAK;QACnC;YAAE,OAAO;YAAW,OAAO;QAAK;QAChC;YAAE,OAAO;YAAU,OAAO;QAAK;QAC/B;YAAE,OAAO;YAAW,OAAO;QAAK;QAChC;YAAE,OAAO;YAAY,OAAO;QAAK;QACjC;YAAE,OAAO;YAAU,OAAO;QAAK;QAC/B;YAAE,OAAO;YAAQ,OAAO;QAAK;KAChC;IAED,yCAAyC;IACzC,MAAM,aAAa;QACf;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;KAChC;IAED,yCAAyC;IACzC,MAAM,sBAAsB;QACxB;YAAE,OAAO;YAA4B,OAAO;QAAW;QACvD;YAAE,OAAO;YAA8B,OAAO;QAAW;QACzD;YAAE,OAAO;YAA0B,OAAO;QAAU;KACvD;IAED,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,8BAA8B;IAC9B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,YAAY,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC,QAAU,OAAO,MAAM,QAAQ;;IAE9D,2BAA2B;IAC3B,MAAM,gBAAgB;QAClB,IAAI;YACA,WAAW;YACX,SAAS;YAET,MAAM,aAAa,IAAI;YACvB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,MAAG,AAAD,EAAE,YAAY,CAAC,GAAG;gBAAE,QAAQ,WAAW,MAAM;YAAC;YAEvE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACnC,YAAY,SAAS,IAAI;gBACzB,0CAA0C;gBAC1C,SAAS,CAAA,GAAA,4HAAA,CAAA,UAAO,AAAD,EAAE,SAAS,IAAI;gBAC9B,iDAAiD;gBACjD,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI;YAC7D,OAAO;gBACH,MAAM,IAAI,MAAM,SAAS,OAAO,IAAI;YACxC;QACJ,EAAE,OAAO,KAAK;YACV,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS,IAAI,OAAO,IAAI;YAExB,2CAA2C;YAC3C,IAAI,WAAW;gBACX,YAAY;YAChB;QACJ,SAAU;YACN,WAAW;QACf;IACJ;IAEA,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACN;QACJ;yCAAG,EAAE;IAEL,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACN,IAAI,UAAU;gBACV,gBAAgB,SAAS,QAAQ,IAAI;gBACrC,gBAAgB,SAAS,QAAQ,IAAI;gBACrC,gBAAgB,SAAS,QAAQ,IAAI;gBACrC,oBAAoB,SAAS,aAAa,IAAI;YAClD;QACJ;yCAAG;QAAC;KAAS;IAEb,yCAAyC;IACzC,MAAM,mBAAmB,CAAC;QACtB,MAAM,OAAO,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;QAC7C,OAAO,OAAO,KAAK,KAAK,GAAG,SAAS;IACxC;IAEA,MAAM,mBAAmB,CAAC;QACtB,MAAM,KAAK,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;QACjD,OAAO,KAAK,GAAG,KAAK,GAAG,SAAS;IACpC;IAEA,MAAM,mBAAmB,CAAC;QACtB,MAAM,OAAO,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;QAC9C,OAAO,OAAO,KAAK,KAAK,GAAG,SAAS;IACxC;IAEA,MAAM,uBAAuB,CAAC;QAC1B,MAAM,SAAS,oBAAoB,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;QACzD,OAAO,SAAS,OAAO,KAAK,GAAG,SAAS;IAC5C;IAEA,MAAM,kBAAkB;QACpB,IAAI,UAAU;YACV,gBAAgB,SAAS,QAAQ,IAAI;YACrC,gBAAgB,SAAS,QAAQ,IAAI;YACrC,gBAAgB,SAAS,QAAQ,IAAI;YACrC,oBAAoB,SAAS,aAAa,IAAI;QAClD;QACA,aAAa;IACjB;IAEA,MAAM,aAAa;QACf,IAAI,CAAC,UAAU;YACX,cAAc;YACd,SAAS;YACT,WAAW,IAAM,cAAc,OAAO;YACtC;QACJ;QAEA,IAAI;YACA,UAAU;YACV,cAAc;YACd,SAAS;YAET,MAAM,aAAa;gBACf,UAAU,gBAAgB;gBAC1B,UAAU,gBAAgB;gBAC1B,UAAU,gBAAgB;gBAC1B,eAAe,oBAAoB;YACvC;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,MAAG,AAAD,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE,EAAE;YAE7D,IAAI,SAAS,OAAO,EAAE;gBAClB,4DAA4D;gBAC5D,IAAI,SAAS,IAAI,EAAE;oBACf,YAAY,SAAS,IAAI;oBACzB,qBAAqB;oBACrB,SAAS,CAAA,GAAA,4HAAA,CAAA,UAAO,AAAD,EAAE,SAAS,IAAI;oBAC9B,sBAAsB;oBACtB,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI;gBAC7D;gBAEA,cAAc;gBACd,aAAa;YACjB,OAAO;gBACH,MAAM,IAAI,MAAM;YACpB;QACJ,EAAE,OAAO,KAAK;YACV,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW,IAAI,OAAO,IAAI;YACnE,cAAc;YACd,SAAS;QACb,SAAU;YACN,UAAU;YACV,WAAW,IAAM,cAAc,OAAO;QAC1C;IACJ;IAEA,MAAM,eAAe;QACjB,aAAa;QACb,IAAI,UAAU;YACV,gBAAgB,SAAS,QAAQ,IAAI;YACrC,gBAAgB,SAAS,QAAQ,IAAI;YACrC,gBAAgB,SAAS,QAAQ,IAAI;YACrC,oBAAoB,SAAS,aAAa,IAAI;QAClD;QACA,SAAS;IACb;IAIA,qBAAqB;IACrB,IAAI,SAAS;QACT,qBACI,6LAAC,qLAAA,CAAA,MAAG;YAAC,IAAI;YAAI,IAAI;YAAI,WAAU;sBAC3B,cAAA,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;0CAAG;;;;;;;;;;;;;;;;kCAGZ,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,+CAA+C;IAC/C,IAAI,CAAC,YAAY,OAAO;QACpB,qBACI,6LAAC,qLAAA,CAAA,MAAG;YAAC,IAAI;YAAI,IAAI;YAAI,WAAU;sBAC3B,cAAA,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;0CAAG;;;;;;;;;;;;;;;;kCAGZ,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAE,OAAO;wCAAE,OAAO;oCAAM;;wCAAG;wCAAQ;;;;;;;8CACpC,6LAAC;oCAAO,WAAU;oCAAY,SAAS;8CAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ9E;IAEA,qBACI,6LAAC,qLAAA,CAAA,MAAG;QAAC,IAAI;QAAI,IAAI;QAAI,WAAU;kBAC3B,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC,6IAAA,CAAA,UAAe;4CACZ,YAAY;4CACZ,OAAO;;;;;;;;;;;;;;;;;;;;;;sCAKvB,6LAAC;4BAAI,WAAU;sCACV,CAAC,2BACE,6LAAC;gCACG,WAAU;gCACV,SAAS;gCACT,UAAU,UAAU;;kDAEpB,6LAAC,uIAAA,CAAA,cAAW;;;;;kDACZ,6LAAC;wCAAK,WAAU;kDAAO;;;;;;;;;;;;;;;;;;;;;;;8BAKvC,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;;kDAEG,6LAAC;;0DACG,6LAAC,qLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAI,IAAI;0DACb,cAAA,6LAAC;8DAAK;;;;;;;;;;;0DAEV,6LAAC,qLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAI,IAAI;0DACZ,0BACG,6LAAC;oDAAI,WAAU;8DACX,cAAA,6LAAC,qLAAA,CAAA,MAAG;kEACA,cAAA,6LAAC,qLAAA,CAAA,MAAG;4DAAC,IAAI;sEACL,cAAA,6LAAC,gJAAA,CAAA,UAAc;gEAEX,SAAS;gEACT,cAAc,iBAAiB;gEAC/B,UAAU,CAAC,MAAQ,gBAAgB,IAAI,KAAK;+DAHvC,CAAC,SAAS,EAAE,gBAAgB,QAAQ;;;;;;;;;;;;;;;;;;;yEASzD,6LAAC;8DAAM,iBAAiB,UAAU;;;;;;;;;;;;;;;;;kDAK9C,6LAAC;;0DACG,6LAAC,qLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAI,IAAI;0DACb,cAAA,6LAAC;8DAAK;;;;;;;;;;;0DAEV,6LAAC,qLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAI,IAAI;0DACZ,0BACG,6LAAC;oDAAI,WAAU;8DACX,cAAA,6LAAC,qLAAA,CAAA,MAAG;kEACA,cAAA,6LAAC,qLAAA,CAAA,MAAG;4DAAC,IAAI;sEACL,cAAA,6LAAC,gJAAA,CAAA,UAAc;gEAEX,SAAS;gEACT,cAAc,iBAAiB;gEAC/B,UAAU,CAAC,MAAQ,gBAAgB,IAAI,KAAK;+DAHvC,CAAC,SAAS,EAAE,gBAAgB,QAAQ;;;;;;;;;;;;;;;;;;;yEASzD,6LAAC;8DAAM,iBAAiB,UAAU;;;;;;;;;;;;;;;;;kDAK9C,6LAAC;;0DACG,6LAAC,qLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAI,IAAI;0DACb,cAAA,6LAAC;8DAAK;;;;;;;;;;;0DAEV,6LAAC,qLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAI,IAAI;0DACZ,0BACG,6LAAC;oDAAI,WAAU;8DACX,cAAA,6LAAC,qLAAA,CAAA,MAAG;kEACA,cAAA,6LAAC,qLAAA,CAAA,MAAG;4DAAC,IAAI;sEACL,cAAA,6LAAC,gJAAA,CAAA,UAAc;gEAEX,SAAS;gEACT,cAAc,iBAAiB;gEAC/B,UAAU,CAAC,MAAQ,gBAAgB,IAAI,KAAK;+DAHvC,CAAC,SAAS,EAAE,gBAAgB,QAAQ;;;;;;;;;;;;;;;;;;;yEASzD,6LAAC;8DAAM,iBAAiB,UAAU;;;;;;;;;;;;;;;;;kDAK9C,6LAAC;;0DACG,6LAAC,qLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAI,IAAI;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAO;;;;;;sEACvB,6LAAC,2IAAA,CAAA,UAAa;4DACV,WAAU;4DACV,uBACI;;kFACI,6LAAC;wEAAE,WAAU;;4EAAO;0FAC4G,6LAAC;gFAAK,WAAU;0FAA4B;;;;;;4EAA6B;;;;;;;kFAEzM,6LAAC;wEAAE,WAAU;;4EAAO;0FAAE,6LAAC;gFAAK,WAAU;0FAA4B;;;;;;4EAAqB;;;;;;;kFACvF,6LAAC;wEAAE,WAAU;;4EAAO;0FAAE,6LAAC;gFAAK,WAAU;0FAA4B;;;;;;4EAAuB;;;;;;;kFACzF,6LAAC;wEAAE,WAAU;;4EAAO;0FACoK,6LAAC;gFAAK,WAAU;0FAA4B;;;;;;;;;;;;kFAEpO,6LAAC;wEAAE,WAAU;kFAAO;;;;;;;;4DAK5B,UAAS;sEAET,cAAA,6LAAC,uIAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;;;;;;0DAI1B,6LAAC,qLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAI,IAAI;0DACZ,0BACG,6LAAC;oDAAI,WAAU;8DACX,cAAA,6LAAC,qLAAA,CAAA,MAAG;kEACA,cAAA,6LAAC,qLAAA,CAAA,MAAG;4DAAC,IAAI;sEACL,cAAA,6LAAC,gJAAA,CAAA,UAAc;gEAEX,SAAS;gEACT,cAAc,qBAAqB;gEACnC,UAAU,CAAC,MAAQ,oBAAoB,IAAI,KAAK;+DAH3C,CAAC,cAAc,EAAE,oBAAoB,QAAQ;;;;;;;;;;;;;;;;;;;yEASlE,6LAAC;8DAAM,qBAAqB,UAAU;;;;;;;;;;;;;;;;;;;;;;;4BAMrD,2BACG,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCACG,WAAU;wCACV,SAAS;wCACT,UAAU;kDAET,SAAS,cAAc;;;;;;kDAE5B,6LAAC;wCACG,WAAU;wCACV,SAAS;wCACT,UAAU;kDACb;;;;;;;;;;;;4BAMR,SAAS,CAAC,2BACP,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCAAE,OAAO;wCAAE,OAAO;wCAAO,UAAU;oCAAO;8CACtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrC;GAhbwB;;QAiEH,4JAAA,CAAA,cAAW;QACV,4JAAA,CAAA,cAAW;;;KAlET", "debugId": null}}, {"offset": {"line": 8335, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8341, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/utils/phoneMask.js"], "sourcesContent": ["/**\r\n * Mask phone number for display\r\n * Shows only last 2 digits: ********01\r\n * Follows project requirements for phone number display\r\n */\r\nexport const maskPhone = (phoneNumber) => {\r\n    if (!phoneNumber) return 'Add Phone Number';\r\n    \r\n    // Remove all non-digit characters\r\n    const cleaned = phoneNumber.replace(/\\D/g, '');\r\n    \r\n    if (cleaned.length < 2) {\r\n        return phoneNumber;\r\n    }\r\n\r\n    const lastTwoDigits = cleaned.slice(-2);\r\n    const starsCount = Math.max(cleaned.length - 2, 8);\r\n    \r\n    return '*'.repeat(starsCount) + lastTwoDigits;\r\n};\r\n\r\n\r\n\r\n/**\r\n * Validate phone number format\r\n */\r\nexport const validatePhoneNumber = (phoneNumber) => {\r\n    if (!phoneNumber || !phoneNumber.trim()) {\r\n        return 'Phone number is required';\r\n    }\r\n    \r\n    // Remove all non-digit characters for validation\r\n    const cleaned = phoneNumber.replace(/\\D/g, '');\r\n    \r\n    if (cleaned.length < 7) {\r\n        return 'Phone number is too short';\r\n    }\r\n    \r\n    if (cleaned.length > 15) {\r\n        return 'Phone number is too long';\r\n    }\r\n    \r\n    return null; // Valid\r\n};\r\n\r\n/**\r\n * Clean phone number (remove all non-digit characters except +)\r\n */\r\nexport const cleanPhoneNumber = (phoneNumber) => {\r\n    if (!phoneNumber) return '';\r\n    return phoneNumber.replace(/[^\\d+]/g, '');\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;AACM,MAAM,YAAY,CAAC;IACtB,IAAI,CAAC,aAAa,OAAO;IAEzB,kCAAkC;IAClC,MAAM,UAAU,YAAY,OAAO,CAAC,OAAO;IAE3C,IAAI,QAAQ,MAAM,GAAG,GAAG;QACpB,OAAO;IACX;IAEA,MAAM,gBAAgB,QAAQ,KAAK,CAAC,CAAC;IACrC,MAAM,aAAa,KAAK,GAAG,CAAC,QAAQ,MAAM,GAAG,GAAG;IAEhD,OAAO,IAAI,MAAM,CAAC,cAAc;AACpC;AAOO,MAAM,sBAAsB,CAAC;IAChC,IAAI,CAAC,eAAe,CAAC,YAAY,IAAI,IAAI;QACrC,OAAO;IACX;IAEA,iDAAiD;IACjD,MAAM,UAAU,YAAY,OAAO,CAAC,OAAO;IAE3C,IAAI,QAAQ,MAAM,GAAG,GAAG;QACpB,OAAO;IACX;IAEA,IAAI,QAAQ,MAAM,GAAG,IAAI;QACrB,OAAO;IACX;IAEA,OAAO,MAAM,QAAQ;AACzB;AAKO,MAAM,mBAAmB,CAAC;IAC7B,IAAI,CAAC,aAAa,OAAO;IACzB,OAAO,YAAY,OAAO,CAAC,WAAW;AAC1C", "debugId": null}}, {"offset": {"line": 8382, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8388, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28User%29/account/details/Partial/PhoneNumber.js"], "sourcesContent": ["'use client';\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Col, Row } from 'react-bootstrap';\r\nimport { PlusIconSvg, EditIconSvg } from '@/assets/svgIcons/SvgIcon';\r\nimport { useSelector } from 'react-redux';\r\nimport { get } from '@/utils/apiUtils';\r\nimport { maskPhone } from '@/utils/phoneMask';\r\nimport Link from 'next/link';\r\n\r\nexport default function PhoneNumber() {\r\n    const [userData, setUserData] = useState(null);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n\r\n    const reduxUser = useSelector((state) => state?.auth?.user || null);\r\n\r\n    // Fetch user data\r\n    const fetchUserData = async () => {\r\n        try {\r\n            setLoading(true);\r\n            setError(null);\r\n\r\n            const controller = new AbortController();\r\n            const response = await get('/account', {}, { signal: controller.signal });\r\n\r\n            if (response.success && response.data) {\r\n                setUserData(response.data);\r\n            } else {\r\n                throw new Error(response.message || 'Failed to fetch user data');\r\n            }\r\n        } catch (err) {\r\n            console.error('Error fetching user data:', err);\r\n            setError(err.message || 'Failed to load phone number information');\r\n\r\n            // Fallback to Redux user data if API fails\r\n            if (reduxUser) {\r\n                setUserData(reduxUser);\r\n            }\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        fetchUserData();\r\n    }, []);\r\n\r\n    const getDisplayPhoneNumber = () => {\r\n        if (!userData?.phone_number) return 'Not set';\r\n        return maskPhone(userData.phone_number);\r\n    };\r\n\r\n    const hasPhoneNumber = userData?.phone_number;\r\n    return (\r\n        <>\r\n            <Col lg={12} xs={12} className=\"mb-3 mb-lg-4\">\r\n                <div className=\"common_blackcard account_card\">\r\n                    <div className=\"common_blackcard_innerheader\">\r\n                        <div className=\"common_blackcard_innerheader_content\">\r\n                            <h6>Phone Number</h6>\r\n                            <p>Provide your number to receive occasional updates, exclusive offers, or important notifications. We will never share your number without consent.</p>\r\n                        </div>\r\n                        <div className=\"common_blackcard_innerheader_icon\">\r\n                            <Link href=\"/account/phone/setup?from=/account/details\" prefetch={true}>\r\n                                <button className=\"d-flex align-items-center\">\r\n                                    {hasPhoneNumber ? <EditIconSvg /> : <PlusIconSvg />}\r\n                                    <span className=\"ms-2\">{hasPhoneNumber ? 'Update' : 'Add Phone Number'}</span>\r\n                                </button>\r\n                            </Link>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"common_blackcard_innerbody\">\r\n                        <div className=\"account_card_list\">\r\n                            {loading ? (\r\n                                <div className=\"text-center py-3\">\r\n                                    <span>Loading phone number...</span>\r\n                                </div>\r\n                            ) : error ? (\r\n                                <div className=\"text-center py-3\">\r\n                                    <span className=\"text-danger\">Failed to load phone number information</span>\r\n                                    <br />\r\n                                    <button\r\n                                        className=\"btn btn-sm btn-link\"\r\n                                        onClick={fetchUserData}\r\n                                        style={{ color: '#007bff', textDecoration: 'underline' }}\r\n                                    >\r\n                                        Retry\r\n                                    </button>\r\n                                </div>\r\n                            ) : (\r\n                                <ul>\r\n                                    <li>\r\n                                        <Col xs={12} md={3}>\r\n                                            <span>Phone Number </span>{\" \"}\r\n                                        </Col>\r\n                                        <Col xs={12} md={9}>\r\n                                            <span>{getDisplayPhoneNumber()}</span>\r\n                                        </Col>\r\n                                    </li>\r\n                                </ul>\r\n                            )}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </Col>\r\n        </>\r\n    )\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;;AASe,SAAS;;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,YAAY,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;8CAAE,CAAC,QAAU,OAAO,MAAM,QAAQ;;IAE9D,kBAAkB;IAClB,MAAM,gBAAgB;QAClB,IAAI;YACA,WAAW;YACX,SAAS;YAET,MAAM,aAAa,IAAI;YACvB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,MAAG,AAAD,EAAE,YAAY,CAAC,GAAG;gBAAE,QAAQ,WAAW,MAAM;YAAC;YAEvE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACnC,YAAY,SAAS,IAAI;YAC7B,OAAO;gBACH,MAAM,IAAI,MAAM,SAAS,OAAO,IAAI;YACxC;QACJ,EAAE,OAAO,KAAK;YACV,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS,IAAI,OAAO,IAAI;YAExB,2CAA2C;YAC3C,IAAI,WAAW;gBACX,YAAY;YAChB;QACJ,SAAU;YACN,WAAW;QACf;IACJ;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN;QACJ;gCAAG,EAAE;IAEL,MAAM,wBAAwB;QAC1B,IAAI,CAAC,UAAU,cAAc,OAAO;QACpC,OAAO,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD,EAAE,SAAS,YAAY;IAC1C;IAEA,MAAM,iBAAiB,UAAU;IACjC,qBACI;kBACI,cAAA,6LAAC,qLAAA,CAAA,MAAG;YAAC,IAAI;YAAI,IAAI;YAAI,WAAU;sBAC3B,cAAA,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAE;;;;;;;;;;;;0CAEP,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAA6C,UAAU;8CAC9D,cAAA,6LAAC;wCAAO,WAAU;;4CACb,+BAAiB,6LAAC,uIAAA,CAAA,cAAW;;;;qEAAM,6LAAC,uIAAA,CAAA,cAAW;;;;;0DAChD,6LAAC;gDAAK,WAAU;0DAAQ,iBAAiB,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKpE,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;sCACV,wBACG,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;8CAAK;;;;;;;;;;uCAEV,sBACA,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAK,WAAU;kDAAc;;;;;;kDAC9B,6LAAC;;;;;kDACD,6LAAC;wCACG,WAAU;wCACV,SAAS;wCACT,OAAO;4CAAE,OAAO;4CAAW,gBAAgB;wCAAY;kDAC1D;;;;;;;;;;;qDAKL,6LAAC;0CACG,cAAA,6LAAC;;sDACG,6LAAC,qLAAA,CAAA,MAAG;4CAAC,IAAI;4CAAI,IAAI;;8DACb,6LAAC;8DAAK;;;;;;gDAAqB;;;;;;;sDAE/B,6LAAC,qLAAA,CAAA,MAAG;4CAAC,IAAI;4CAAI,IAAI;sDACb,cAAA,6LAAC;0DAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnD;GAlGwB;;QAKF,4JAAA,CAAA,cAAW;;;KALT", "debugId": null}}, {"offset": {"line": 8665, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8671, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/utils/emailMask.js"], "sourcesContent": ["export function maskEmail(email) {\r\n    if (!email) return \"\";\r\n\r\n    const parts = email.split(\"@\");\r\n    if (parts.length !== 2) return email;\r\n\r\n    const username = parts[0];\r\n    const domain = parts[1];\r\n\r\n    if (username.length <= 1) {\r\n        return \"**@\" + domain;\r\n    }\r\n\r\n    const lastChar = username[username.length - 1];\r\n    return \"**\" + last<PERSON>har + \"@\" + domain;\r\n}\r\n\r\n"], "names": [], "mappings": ";;;AAAO,SAAS,UAAU,KAAK;IAC3B,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,QAAQ,MAAM,KAAK,CAAC;IAC1B,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;IAE/B,MAAM,WAAW,KAAK,CAAC,EAAE;IACzB,MAAM,SAAS,KAAK,CAAC,EAAE;IAEvB,IAAI,SAAS,MAAM,IAAI,GAAG;QACtB,OAAO,QAAQ;IACnB;IAEA,MAAM,WAAW,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;IAC9C,OAAO,OAAO,WAAW,MAAM;AACnC", "debugId": null}}, {"offset": {"line": 8689, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8695, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28User%29/account/details/Partial/Email.js"], "sourcesContent": ["'use client';\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Col } from 'react-bootstrap';\r\nimport { EditIconSvg } from '@/assets/svgIcons/SvgIcon';\r\nimport Link from 'next/link';\r\nimport { maskEmail } from '@/utils/emailMask';\r\nimport { useSelector, useDispatch } from 'react-redux';\r\nimport { setUser } from '@/redux/authSlice';\r\nimport { get } from '@/utils/apiUtils';\r\nimport { usePathname } from 'next/navigation';\r\n\r\n\r\nexport default function Email() {\r\n    const [userData, setUserData] = useState(null);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n\r\n    const dispatch = useDispatch();\r\n    const reduxUser = useSelector((state) => state?.auth?.user || null);\r\n    const pathname = usePathname();\r\n\r\n    // Fetch user data from API\r\n    const fetchUserData = async () => {\r\n        try {\r\n            setLoading(true);\r\n            setError(null);\r\n\r\n            const controller = new AbortController();\r\n            const response = await get('/account', {}, { signal: controller.signal });\r\n\r\n            if (response.success && response.data) {\r\n                setUserData(response.data);\r\n                // Update Redux store with fresh user data\r\n                dispatch(setUser(response.data));\r\n                // Also update localStorage to ensure consistency\r\n                localStorage.setItem('user', JSON.stringify(response.data));\r\n            } else {\r\n                throw new Error(response.message || 'Failed to fetch user data');\r\n            }\r\n        } catch (err) {\r\n            console.error('Error fetching user data:', err);\r\n            setError(err.message || 'Failed to load user information');\r\n\r\n            // Fallback to Redux user data if API fails\r\n            if (reduxUser) {\r\n                setUserData(reduxUser);\r\n            }\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        // Always fetch fresh data first to ensure we have the latest from DB\r\n        fetchUserData();\r\n\r\n        // Also check for cached data as fallback\r\n        const storedUser = localStorage.getItem('user');\r\n\r\n        if (reduxUser) {\r\n            // Use Redux data if available, but still fetch fresh data\r\n            setUserData(reduxUser);\r\n            setLoading(false);\r\n        } else if (storedUser) {\r\n            // Use localStorage data as immediate fallback while API loads\r\n            try {\r\n                const parsedUser = JSON.parse(storedUser);\r\n                setUserData(parsedUser);\r\n                // Update Redux store\r\n                dispatch(setUser(parsedUser));\r\n                setLoading(false);\r\n            } catch (err) {\r\n                console.error('Error parsing stored user data:', err);\r\n            }\r\n        }\r\n    }, []); // Empty dependency array to run only on mount\r\n\r\n    const getDisplayEmail = () => {\r\n        if (!userData?.email) return 'Not set';\r\n        return maskEmail(userData.email);\r\n    };\r\n\r\n    if (loading) {\r\n        return (\r\n            <Col lg={12} xs={12} className=\"mb-3 mb-lg-4\">\r\n                <div className=\"common_blackcard account_card\">\r\n                    <div className=\"common_blackcard_innerheader\">\r\n                        <div className=\"common_blackcard_innerheader_content\">\r\n                            <h6>Email</h6>\r\n                        </div>\r\n                        <div className=\"common_blackcard_innerheader_icon\">\r\n                            <Link href={`/account/email/setup?from=${encodeURIComponent(pathname)}`} prefetch={true}>\r\n                                <button className=\"d-flex align-items-center\">\r\n                                    <EditIconSvg />\r\n                                    <span className=\"ms-2\">Update</span>\r\n                                </button>\r\n                            </Link>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"common_blackcard_innerbody\">\r\n                        <div className=\"account_card_list\">\r\n                            <ul>\r\n                                <li>\r\n                                    <Col xs={12} md={3}>\r\n                                        <span>Email </span>\r\n                                    </Col>\r\n                                    <Col xs={12} md={3}>\r\n                                        <span>Loading...</span>\r\n                                    </Col>\r\n                                </li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </Col>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <>\r\n            <Col lg={12} xs={12} className=\"mb-3 mb-lg-4\">\r\n                <div className=\"common_blackcard account_card\">\r\n                    <div className=\"common_blackcard_innerheader\">\r\n                        <div className=\"common_blackcard_innerheader_content\">\r\n                            <h6>Email</h6>\r\n                        </div>\r\n                        <div className=\"common_blackcard_innerheader_icon\">\r\n                            <Link href={`/account/email/setup?from=${encodeURIComponent(pathname)}`} prefetch={true}>\r\n                                <button className=\"d-flex align-items-center\">\r\n                                    <EditIconSvg />\r\n                                    <span className=\"ms-2\">Update</span>\r\n                                </button>\r\n                            </Link>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"common_blackcard_innerbody\">\r\n                        <div className=\"account_card_list\">\r\n                            <ul>\r\n                                <li>\r\n                                    <Col xs={12} md={3}>\r\n                                        <span>Email </span>\r\n                                    </Col>\r\n                                    <Col xs={12} md={3}>\r\n                                        <span>{getDisplayEmail()}</span>\r\n                                    </Col>\r\n                                </li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </Col >\r\n        </>\r\n    )\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;;AAYe,SAAS;;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,YAAY,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;wCAAE,CAAC,QAAU,OAAO,MAAM,QAAQ;;IAC9D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,2BAA2B;IAC3B,MAAM,gBAAgB;QAClB,IAAI;YACA,WAAW;YACX,SAAS;YAET,MAAM,aAAa,IAAI;YACvB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,MAAG,AAAD,EAAE,YAAY,CAAC,GAAG;gBAAE,QAAQ,WAAW,MAAM;YAAC;YAEvE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACnC,YAAY,SAAS,IAAI;gBACzB,0CAA0C;gBAC1C,SAAS,CAAA,GAAA,4HAAA,CAAA,UAAO,AAAD,EAAE,SAAS,IAAI;gBAC9B,iDAAiD;gBACjD,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI;YAC7D,OAAO;gBACH,MAAM,IAAI,MAAM,SAAS,OAAO,IAAI;YACxC;QACJ,EAAE,OAAO,KAAK;YACV,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS,IAAI,OAAO,IAAI;YAExB,2CAA2C;YAC3C,IAAI,WAAW;gBACX,YAAY;YAChB;QACJ,SAAU;YACN,WAAW;QACf;IACJ;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACN,qEAAqE;YACrE;YAEA,yCAAyC;YACzC,MAAM,aAAa,aAAa,OAAO,CAAC;YAExC,IAAI,WAAW;gBACX,0DAA0D;gBAC1D,YAAY;gBACZ,WAAW;YACf,OAAO,IAAI,YAAY;gBACnB,8DAA8D;gBAC9D,IAAI;oBACA,MAAM,aAAa,KAAK,KAAK,CAAC;oBAC9B,YAAY;oBACZ,qBAAqB;oBACrB,SAAS,CAAA,GAAA,4HAAA,CAAA,UAAO,AAAD,EAAE;oBACjB,WAAW;gBACf,EAAE,OAAO,KAAK;oBACV,QAAQ,KAAK,CAAC,mCAAmC;gBACrD;YACJ;QACJ;0BAAG,EAAE,GAAG,8CAA8C;IAEtD,MAAM,kBAAkB;QACpB,IAAI,CAAC,UAAU,OAAO,OAAO;QAC7B,OAAO,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD,EAAE,SAAS,KAAK;IACnC;IAEA,IAAI,SAAS;QACT,qBACI,6LAAC,qLAAA,CAAA,MAAG;YAAC,IAAI;YAAI,IAAI;YAAI,WAAU;sBAC3B,cAAA,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;8CAAG;;;;;;;;;;;0CAER,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAM,CAAC,0BAA0B,EAAE,mBAAmB,WAAW;oCAAE,UAAU;8CAC/E,cAAA,6LAAC;wCAAO,WAAU;;0DACd,6LAAC,uIAAA,CAAA,cAAW;;;;;0DACZ,6LAAC;gDAAK,WAAU;0DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKvC,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;0CACG,cAAA,6LAAC;;sDACG,6LAAC,qLAAA,CAAA,MAAG;4CAAC,IAAI;4CAAI,IAAI;sDACb,cAAA,6LAAC;0DAAK;;;;;;;;;;;sDAEV,6LAAC,qLAAA,CAAA,MAAG;4CAAC,IAAI;4CAAI,IAAI;sDACb,cAAA,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAS1C;IAEA,qBACI;kBACI,cAAA,6LAAC,qLAAA,CAAA,MAAG;YAAC,IAAI;YAAI,IAAI;YAAI,WAAU;sBAC3B,cAAA,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;8CAAG;;;;;;;;;;;0CAER,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAM,CAAC,0BAA0B,EAAE,mBAAmB,WAAW;oCAAE,UAAU;8CAC/E,cAAA,6LAAC;wCAAO,WAAU;;0DACd,6LAAC,uIAAA,CAAA,cAAW;;;;;0DACZ,6LAAC;gDAAK,WAAU;0DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKvC,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;0CACG,cAAA,6LAAC;;sDACG,6LAAC,qLAAA,CAAA,MAAG;4CAAC,IAAI;4CAAI,IAAI;sDACb,cAAA,6LAAC;0DAAK;;;;;;;;;;;sDAEV,6LAAC,qLAAA,CAAA,MAAG;4CAAC,IAAI;4CAAI,IAAI;sDACb,cAAA,6LAAC;0DAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU/C;GA7IwB;;QAKH,4JAAA,CAAA,cAAW;QACV,4JAAA,CAAA,cAAW;QACZ,qIAAA,CAAA,cAAW;;;KAPR", "debugId": null}}, {"offset": {"line": 9077, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9083, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28User%29/account/details/Partial/Username.js"], "sourcesContent": ["'use client';\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Col } from 'react-bootstrap';\r\nimport { EditIconSvg, SolidInfoIcon } from '@/assets/svgIcons/SvgIcon';\r\nimport Link from 'next/link';\r\nimport CommonTooltip from '@/Components/UI/CommonTooltip';\r\nimport { useSelector, useDispatch } from 'react-redux';\r\nimport { setUser } from '@/redux/authSlice';\r\nimport { get } from '@/utils/apiUtils';\r\n\r\nexport default function Username() {\r\n    const [userData, setUserData] = useState(null);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n    const reduxUser = useSelector((state) => state?.auth?.user || null);\r\n    const dispatch = useDispatch();\r\n\r\n    // Fetch user data from API\r\n    const fetchUserData = async () => {\r\n        try {\r\n            setLoading(true);\r\n            setError(null);\r\n\r\n            const controller = new AbortController();\r\n            const response = await get('/account', {}, { signal: controller.signal });\r\n\r\n            if (response.success && response.data) {\r\n                setUserData(response.data);\r\n                // Update Redux store with fresh user data\r\n                dispatch(setUser(response.data));\r\n                // Also update localStorage to ensure consistency\r\n                localStorage.setItem('user', JSON.stringify(response.data));\r\n            } else {\r\n                throw new Error(response.message || 'Failed to fetch user data');\r\n            }\r\n        } catch (err) {\r\n            console.error('Error fetching user data:', err);\r\n            setError(err.message || 'Failed to load user information');\r\n\r\n            // Fallback to Redux user data if API fails\r\n            if (reduxUser) {\r\n                setUserData(reduxUser);\r\n            }\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        fetchUserData();\r\n    }, []);\r\n\r\n    const getDisplayUsername = () => {\r\n        if (!userData) return 'Loading...';\r\n        if (!userData.username) return 'Not set';\r\n\r\n        // Display username as static text\r\n        return userData.username;\r\n    };\r\n\r\n    const getChangesRemaining = () => {\r\n        if (!userData) return 2;\r\n        const changesUsed = userData.username_change_count || 0;\r\n        return Math.max(0, 2 - changesUsed);\r\n    };\r\n\r\n    const hasChangesRemaining = () => {\r\n        return getChangesRemaining() > 0;\r\n    };\r\n\r\n    const getDescriptionText = () => {\r\n        const remaining = getChangesRemaining();\r\n        if (remaining === 2) {\r\n            return \"Your public username may be changed two times.\";\r\n        } else if (remaining === 1) {\r\n            return \"Your public username may be changed one more time.\";\r\n        } else {\r\n            return \"You have used all available username changes.\";\r\n        }\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <Col lg={12} xs={12} className=\"mb-3 mb-lg-4\">\r\n                <div className=\"common_blackcard account_card\">\r\n                    <div className=\"common_blackcard_innerheader\">\r\n                        <div className=\"common_blackcard_innerheader_content\">\r\n                            <h6>Username</h6>\r\n                            <p>{getDescriptionText()}</p>\r\n                        </div>\r\n                        <div className=\"common_blackcard_innerheader_icon\">\r\n                            {hasChangesRemaining() ? (\r\n                                <Link href={`/account/username/setup?from=${encodeURIComponent('/account/details')}`} prefetch={true}>\r\n                                    <button className=\"d-flex align-items-center\">\r\n                                        <EditIconSvg />\r\n                                        <span className=\"ms-2\">Update</span>\r\n                                    </button>\r\n                                </Link>\r\n                            ) : (\r\n                                <CommonTooltip\r\n                                    className=\"CustomTooltip\"\r\n                                    content={\r\n                                        <>\r\n                                            <p>Username changes are limited to 2 per account to protect identity and prevent abuse. You've reached that limit.</p>\r\n                                            <p>\r\n                                                If you need another change, you can request one through our <Link href=\"/help\" prefetch={true}>support team.</Link>\r\n                                            </p>\r\n                                        </>\r\n                                    }\r\n                                    position=\"top-right\"\r\n                                >\r\n                                    <SolidInfoIcon />\r\n                                </CommonTooltip>\r\n                            )}\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"common_blackcard_innerbody\">\r\n                        <div className=\"account_card_list\">\r\n                            {loading ? (\r\n                                <div className=\"text-center py-3\">\r\n                                    <span>Loading username information...</span>\r\n                                </div>\r\n                            ) : error ? (\r\n                                <div className=\"text-center py-3\">\r\n                                    <span className=\"text-danger\">Failed to load username information</span>\r\n                                    <br />\r\n                                    <button\r\n                                        className=\"btn btn-sm btn-link\"\r\n                                        onClick={fetchUserData}\r\n                                        style={{ color: '#007bff', textDecoration: 'underline' }}\r\n                                    >\r\n                                        Retry\r\n                                    </button>\r\n                                </div>\r\n                            ) : (\r\n                                <ul>\r\n                                    <li>\r\n                                        <Col xs={12} md={3}>\r\n                                            <span>Username </span>\r\n                                        </Col>\r\n                                        <Col xs={12} md={9}>\r\n                                            <span>{getDisplayUsername()}</span>\r\n                                        </Col>\r\n                                    </li>\r\n                                </ul>\r\n                            )}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </Col >\r\n        </>\r\n    )\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;;AAUe,SAAS;;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,YAAY,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;2CAAE,CAAC,QAAU,OAAO,MAAM,QAAQ;;IAC9D,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAE3B,2BAA2B;IAC3B,MAAM,gBAAgB;QAClB,IAAI;YACA,WAAW;YACX,SAAS;YAET,MAAM,aAAa,IAAI;YACvB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,MAAG,AAAD,EAAE,YAAY,CAAC,GAAG;gBAAE,QAAQ,WAAW,MAAM;YAAC;YAEvE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACnC,YAAY,SAAS,IAAI;gBACzB,0CAA0C;gBAC1C,SAAS,CAAA,GAAA,4HAAA,CAAA,UAAO,AAAD,EAAE,SAAS,IAAI;gBAC9B,iDAAiD;gBACjD,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI;YAC7D,OAAO;gBACH,MAAM,IAAI,MAAM,SAAS,OAAO,IAAI;YACxC;QACJ,EAAE,OAAO,KAAK;YACV,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS,IAAI,OAAO,IAAI;YAExB,2CAA2C;YAC3C,IAAI,WAAW;gBACX,YAAY;YAChB;QACJ,SAAU;YACN,WAAW;QACf;IACJ;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACN;QACJ;6BAAG,EAAE;IAEL,MAAM,qBAAqB;QACvB,IAAI,CAAC,UAAU,OAAO;QACtB,IAAI,CAAC,SAAS,QAAQ,EAAE,OAAO;QAE/B,kCAAkC;QAClC,OAAO,SAAS,QAAQ;IAC5B;IAEA,MAAM,sBAAsB;QACxB,IAAI,CAAC,UAAU,OAAO;QACtB,MAAM,cAAc,SAAS,qBAAqB,IAAI;QACtD,OAAO,KAAK,GAAG,CAAC,GAAG,IAAI;IAC3B;IAEA,MAAM,sBAAsB;QACxB,OAAO,wBAAwB;IACnC;IAEA,MAAM,qBAAqB;QACvB,MAAM,YAAY;QAClB,IAAI,cAAc,GAAG;YACjB,OAAO;QACX,OAAO,IAAI,cAAc,GAAG;YACxB,OAAO;QACX,OAAO;YACH,OAAO;QACX;IACJ;IAEA,qBACI;kBACI,cAAA,6LAAC,qLAAA,CAAA,MAAG;YAAC,IAAI;YAAI,IAAI;YAAI,WAAU;sBAC3B,cAAA,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;;;;;;;0CAER,6LAAC;gCAAI,WAAU;0CACV,sCACG,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAM,CAAC,6BAA6B,EAAE,mBAAmB,qBAAqB;oCAAE,UAAU;8CAC5F,cAAA,6LAAC;wCAAO,WAAU;;0DACd,6LAAC,uIAAA,CAAA,cAAW;;;;;0DACZ,6LAAC;gDAAK,WAAU;0DAAO;;;;;;;;;;;;;;;;yDAI/B,6LAAC,2IAAA,CAAA,UAAa;oCACV,WAAU;oCACV,uBACI;;0DACI,6LAAC;0DAAE;;;;;;0DACH,6LAAC;;oDAAE;kEAC6D,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAQ,UAAU;kEAAM;;;;;;;;;;;;;;oCAI3G,UAAS;8CAET,cAAA,6LAAC,uIAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;;;;;;kCAK9B,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;sCACV,wBACG,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;8CAAK;;;;;;;;;;uCAEV,sBACA,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAK,WAAU;kDAAc;;;;;;kDAC9B,6LAAC;;;;;kDACD,6LAAC;wCACG,WAAU;wCACV,SAAS;wCACT,OAAO;4CAAE,OAAO;4CAAW,gBAAgB;wCAAY;kDAC1D;;;;;;;;;;;qDAKL,6LAAC;0CACG,cAAA,6LAAC;;sDACG,6LAAC,qLAAA,CAAA,MAAG;4CAAC,IAAI;4CAAI,IAAI;sDACb,cAAA,6LAAC;0DAAK;;;;;;;;;;;sDAEV,6LAAC,qLAAA,CAAA,MAAG;4CAAC,IAAI;4CAAI,IAAI;sDACb,cAAA,6LAAC;0DAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnD;GA9IwB;;QAIF,4JAAA,CAAA,cAAW;QACZ,4JAAA,CAAA,cAAW;;;KALR", "debugId": null}}, {"offset": {"line": 9421, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9427, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/utils/addressMask.js"], "sourcesContent": ["/**\r\n * Mask name for display\r\n * Shows only first letter + asterisks: A**** M*****\r\n * Follows project requirements for name display\r\n */\r\nexport const maskName = (name) => {\r\n    if (!name || name.length === 0) return '';\r\n    if (name.length === 1) return name;\r\n    return name[0] + '*'.repeat(name.length - 1);\r\n};\r\n\r\n/**\r\n * Mask full name (first and last name)\r\n * Shows only first letter of each part + asterisks: A**** M*****\r\n */\r\nexport const maskFullName = (fullName) => {\r\n    if (!fullName) return '';\r\n    \r\n    const parts = fullName.split(' ');\r\n    return parts\r\n        .map((part) => {\r\n            if (part.length <= 1) return part;\r\n            return part[0] + '*'.repeat(part.length - 1);\r\n        })\r\n        .join(' ');\r\n};\r\n\r\n/**\r\n * Mask address for display\r\n * Shows only first character + asterisks: 2****************\r\n * Follows project requirements for address display\r\n */\r\nexport const maskAddress = (address) => {\r\n    if (!address || address.length === 0) return '';\r\n    if (address.length === 1) return address;\r\n    return address[0] + '*'.repeat(address.length - 1);\r\n};\r\n\r\n/**\r\n * Format address for display\r\n * Combines city, state, and zip code\r\n */\r\nexport const formatCityStateZip = (city, state, zipCode) => {\r\n    const parts = [city, state, zipCode].filter(Boolean);\r\n    return parts.join(', ');\r\n};\r\n\r\n/**\r\n * Get display name for address\r\n * Returns masked full name or \"Add Name\" if empty\r\n */\r\nexport const getDisplayName = (firstName, lastName) => {\r\n    if (!firstName && !lastName) return '+ Add Name';\r\n    \r\n    const fullName = [firstName, lastName].filter(Boolean).join(' ');\r\n    return maskFullName(fullName);\r\n};\r\n\r\n/**\r\n * Get display address\r\n * Returns masked address or \"Add Address\" if empty\r\n */\r\nexport const getDisplayAddress = (address) => {\r\n    if (!address) return '+ Add Address';\r\n    return maskAddress(address);\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;AACM,MAAM,WAAW,CAAC;IACrB,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG,OAAO;IACvC,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;IAC9B,OAAO,IAAI,CAAC,EAAE,GAAG,IAAI,MAAM,CAAC,KAAK,MAAM,GAAG;AAC9C;AAMO,MAAM,eAAe,CAAC;IACzB,IAAI,CAAC,UAAU,OAAO;IAEtB,MAAM,QAAQ,SAAS,KAAK,CAAC;IAC7B,OAAO,MACF,GAAG,CAAC,CAAC;QACF,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;QAC7B,OAAO,IAAI,CAAC,EAAE,GAAG,IAAI,MAAM,CAAC,KAAK,MAAM,GAAG;IAC9C,GACC,IAAI,CAAC;AACd;AAOO,MAAM,cAAc,CAAC;IACxB,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG,OAAO;IAC7C,IAAI,QAAQ,MAAM,KAAK,GAAG,OAAO;IACjC,OAAO,OAAO,CAAC,EAAE,GAAG,IAAI,MAAM,CAAC,QAAQ,MAAM,GAAG;AACpD;AAMO,MAAM,qBAAqB,CAAC,MAAM,OAAO;IAC5C,MAAM,QAAQ;QAAC;QAAM;QAAO;KAAQ,CAAC,MAAM,CAAC;IAC5C,OAAO,MAAM,IAAI,CAAC;AACtB;AAMO,MAAM,iBAAiB,CAAC,WAAW;IACtC,IAAI,CAAC,aAAa,CAAC,UAAU,OAAO;IAEpC,MAAM,WAAW;QAAC;QAAW;KAAS,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;IAC5D,OAAO,aAAa;AACxB;AAMO,MAAM,oBAAoB,CAAC;IAC9B,IAAI,CAAC,SAAS,OAAO;IACrB,OAAO,YAAY;AACvB", "debugId": null}}, {"offset": {"line": 9480, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9486, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28User%29/account/details/Partial/AddressBook.js"], "sourcesContent": ["'use client';\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Col } from 'react-bootstrap';\r\nimport { EditIconSvg, PlusIconSvg, CheckIcon, RemoveIconSvg } from '@/assets/svgIcons/SvgIcon';\r\nimport Link from 'next/link';\r\nimport { get } from '@/utils/apiUtils';\r\nimport { maskFullName, maskAddress } from '@/utils/addressMask';\r\n\r\nexport default function AddressBook() {\r\n    const [addresses, setAddresses] = useState([]);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n\r\n    const fetchAddresses = async () => {\r\n        try {\r\n            setLoading(true);\r\n\r\n            setError(null);\r\n\r\n            const controller = new AbortController();\r\n            const response = await get('/addresses', {}, { signal: controller.signal });\r\n\r\n            if (response.success && response.data) {\r\n                setAddresses(response.data);\r\n            } else {\r\n                throw new Error(response.message || 'Failed to fetch addresses');\r\n            }\r\n        } catch (err) {\r\n            console.error('Error fetching addresses:', err);\r\n            setError(err.message || 'Failed to load addresses');\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n    useEffect(() => {\r\n        fetchAddresses();\r\n    }, []);\r\n\r\n    return (\r\n        <>\r\n            <Col lg={12} xs={12} className=\"mb-3 mb-lg-4\">\r\n                <div className=\"common_blackcard account_card\">\r\n                    <div className=\"common_blackcard_innerheader\">\r\n                        <div className=\"common_blackcard_innerheader_content\">\r\n                            <div className=\"account_header_main\">\r\n                                <h6>Address Book</h6>\r\n                            </div>\r\n                        </div>\r\n                        <div className=\"common_blackcard_innerheader_icon\">\r\n                            <Link href=\"/account/address/setup\" prefetch={true}>\r\n                                <button className=\"d-flex align-items-center\">\r\n                                    <PlusIconSvg />\r\n                                    <span className=\"ms-2\">Add New Address</span>\r\n                                </button>\r\n                            </Link>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div className=\"common_blackcard_innerbody\">\r\n                        {loading ? (\r\n                            <div className=\"text-center py-4\">\r\n                                <p>Loading addresses...</p>\r\n                            </div>\r\n                        ) : error ? (\r\n                            <div className=\"text-center py-4\">\r\n                                <p className=\"text-danger\">{error}</p>\r\n                                <button\r\n                                    onClick={fetchAddresses}\r\n                                    className=\"btn btn-sm btn-outline-primary mt-2\"\r\n                                >\r\n                                    Try Again\r\n                                </button>\r\n                            </div>\r\n                        ) : addresses.length === 0 ? (\r\n                            <div className=\"text-center py-4\">\r\n                                <p>No addresses found. Add your first address to get started.</p>\r\n                            </div>\r\n                        ) : (\r\n                            <div className=\"account_card_list new-address-section\">\r\n                                <ul>\r\n                                    {addresses.map((addr) => (\r\n                                        <li key={addr.id}>\r\n                                            <Col xs={12} md={3}>\r\n                                                {addr.is_default ? (\r\n                                                    <div className=\"d-flex align-items-center gap-2\">\r\n                                                        <CheckIcon />\r\n                                                        <span className=\"green_text\">Default</span>\r\n                                                    </div>\r\n                                                ) : (\r\n                                                    <Link href={`/account/address/manage?id=${addr.id}`} prefetch={true}>\r\n                                                        <button className=\"btn btn-link p-0 set-default-btn\">\r\n                                                            <span className='text_00ADEF'>Set as Default</span>\r\n                                                        </button>\r\n                                                    </Link>\r\n                                                )}\r\n                                            </Col>\r\n                                            <Col xs={12} md={9}>\r\n                                                <div className='d-flex justify-content-between align-items-center w-100'>\r\n                                                    <div className='show-address-details'>\r\n                                                        <p className='name'>{maskFullName(addr.full_name)}</p>\r\n                                                        <p className='address'>{maskAddress(addr.address)}</p>\r\n                                                        <p className='city'>{addr.city}, {addr.state}, {addr.zip_code}</p>\r\n                                                    </div>\r\n\r\n                                                    <div className='btns d-flex gap-2'>\r\n                                                        {!addr.is_default && (\r\n                                                            <>\r\n                                                                <Link href={`/account/address/manage`} prefetch={true}>\r\n                                                                    <button className=\"d-flex align-items-center\">\r\n                                                                        <RemoveIconSvg />\r\n                                                                        <span className=\"ms-1\">Remove</span>\r\n                                                                    </button>\r\n                                                                </Link>\r\n                                                                <Link href={`/account/address/manage`} prefetch={true}>\r\n                                                                    <button className=\"d-flex align-items-center\">\r\n                                                                        <EditIconSvg />\r\n                                                                        <span className=\"ms-1\">Edit</span>\r\n                                                                    </button>\r\n                                                                </Link>\r\n                                                            </>\r\n                                                        )}\r\n\r\n                                                        {addr.is_default && (\r\n                                                            <Link href={`/account/address/manage`} prefetch={true}>\r\n                                                                <button className=\"d-flex align-items-center\">\r\n                                                                    <EditIconSvg />\r\n                                                                    <span className=\"ms-1\">Edit</span>\r\n                                                                </button>\r\n                                                            </Link>\r\n                                                        )}\r\n                                                    </div>\r\n                                                </div>\r\n                                            </Col>\r\n                                        </li>\r\n                                    ))}\r\n                                </ul>\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n            </Col>\r\n        </>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;;;AANA;;;;;;;AAQe,SAAS;;IACpB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,iBAAiB;QACnB,IAAI;YACA,WAAW;YAEX,SAAS;YAET,MAAM,aAAa,IAAI;YACvB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,MAAG,AAAD,EAAE,cAAc,CAAC,GAAG;gBAAE,QAAQ,WAAW,MAAM;YAAC;YAEzE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACnC,aAAa,SAAS,IAAI;YAC9B,OAAO;gBACH,MAAM,IAAI,MAAM,SAAS,OAAO,IAAI;YACxC;QACJ,EAAE,OAAO,KAAK;YACV,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS,IAAI,OAAO,IAAI;QAC5B,SAAU;YACN,WAAW;QACf;IACJ;IACA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN;QACJ;gCAAG,EAAE;IAEL,qBACI;kBACI,cAAA,6LAAC,qLAAA,CAAA,MAAG;YAAC,IAAI;YAAI,IAAI;YAAI,WAAU;sBAC3B,cAAA,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC;kDAAG;;;;;;;;;;;;;;;;0CAGZ,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAyB,UAAU;8CAC1C,cAAA,6LAAC;wCAAO,WAAU;;0DACd,6LAAC,uIAAA,CAAA,cAAW;;;;;0DACZ,6LAAC;gDAAK,WAAU;0DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMvC,6LAAC;wBAAI,WAAU;kCACV,wBACG,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;0CAAE;;;;;;;;;;mCAEP,sBACA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAE,WAAU;8CAAe;;;;;;8CAC5B,6LAAC;oCACG,SAAS;oCACT,WAAU;8CACb;;;;;;;;;;;mCAIL,UAAU,MAAM,KAAK,kBACrB,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;0CAAE;;;;;;;;;;iDAGP,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;0CACI,UAAU,GAAG,CAAC,CAAC,qBACZ,6LAAC;;0DACG,6LAAC,qLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAI,IAAI;0DACZ,KAAK,UAAU,iBACZ,6LAAC;oDAAI,WAAU;;sEACX,6LAAC,uIAAA,CAAA,YAAS;;;;;sEACV,6LAAC;4DAAK,WAAU;sEAAa;;;;;;;;;;;yEAGjC,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAM,CAAC,2BAA2B,EAAE,KAAK,EAAE,EAAE;oDAAE,UAAU;8DAC3D,cAAA,6LAAC;wDAAO,WAAU;kEACd,cAAA,6LAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;;;;;;;;;;0DAK9C,6LAAC,qLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAI,IAAI;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEAAE,WAAU;8EAAQ,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,KAAK,SAAS;;;;;;8EAChD,6LAAC;oEAAE,WAAU;8EAAW,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE,KAAK,OAAO;;;;;;8EAChD,6LAAC;oEAAE,WAAU;;wEAAQ,KAAK,IAAI;wEAAC;wEAAG,KAAK,KAAK;wEAAC;wEAAG,KAAK,QAAQ;;;;;;;;;;;;;sEAGjE,6LAAC;4DAAI,WAAU;;gEACV,CAAC,KAAK,UAAU,kBACb;;sFACI,6LAAC,+JAAA,CAAA,UAAI;4EAAC,MAAM,CAAC,uBAAuB,CAAC;4EAAE,UAAU;sFAC7C,cAAA,6LAAC;gFAAO,WAAU;;kGACd,6LAAC,uIAAA,CAAA,gBAAa;;;;;kGACd,6LAAC;wFAAK,WAAU;kGAAO;;;;;;;;;;;;;;;;;sFAG/B,6LAAC,+JAAA,CAAA,UAAI;4EAAC,MAAM,CAAC,uBAAuB,CAAC;4EAAE,UAAU;sFAC7C,cAAA,6LAAC;gFAAO,WAAU;;kGACd,6LAAC,uIAAA,CAAA,cAAW;;;;;kGACZ,6LAAC;wFAAK,WAAU;kGAAO;;;;;;;;;;;;;;;;;;;gEAMtC,KAAK,UAAU,kBACZ,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAM,CAAC,uBAAuB,CAAC;oEAAE,UAAU;8EAC7C,cAAA,6LAAC;wEAAO,WAAU;;0FACd,6LAAC,uIAAA,CAAA,cAAW;;;;;0FACZ,6LAAC;gFAAK,WAAU;0FAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA7C1C,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DxD;GAvIwB;KAAA", "debugId": null}}, {"offset": {"line": 9920, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9926, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28User%29/account/details/page.js"], "sourcesContent": ["'use client';\r\nimport { Row } from \"react-bootstrap\";\r\nimport React from 'react'\r\nimport AccountLayout from \"@/Layouts/AccountLayout\";\r\nimport MetaHead from \"@/Seo/Meta/MetaHead\";\r\nimport SidebarHeading from \"@/Components/common/Account/SidebarHeading\";\r\nimport \"@/css/account/AccountDetails.scss\";\r\nimport PersonalInformation from \"./Partial/PersonalInformation\";\r\nimport LocalizationSettings from \"./Partial/LocalizationSettings\";\r\nimport PhoneNumber from \"./Partial/PhoneNumber\";\r\nimport Email from \"./Partial/Email\";\r\nimport Username from \"./Partial/Username\";\r\nimport AddressBook from \"./Partial/AddressBook\";\r\n\r\nexport default function AcoountDetails() {\r\n  const metaArray = {\r\n    noindex: true,\r\n    title: \"Account Details | Update Info | TradeReply\",\r\n    description: \"Update your personal information on TradeReply.com. Manage your phone number, email, language, time zone, and other account settings.\",\r\n    canonical_link: \"https://www.tradereply.com/account/details\",\r\n    og_site_name: \"TradeReply\",\r\n    og_title: \"Account Details | Update Info | TradeReply\",\r\n    og_description: \"Update your personal information on TradeReply. Manage your phone number, email, language, time zone, and other account settings.\",\r\n    twitter_title: \"Account Details | Update Info | TradeReply\",\r\n    twitter_description: \"Update your personal information on TradeReply. Manage your phone number, email, language, time zone, and other account settings.\",\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <AccountLayout>\r\n        <MetaHead props={metaArray} />\r\n        <div className=\"account_details\">\r\n          <SidebarHeading title=\"Account Details\" />\r\n          <Row>\r\n            <PersonalInformation />\r\n            <LocalizationSettings />\r\n            <PhoneNumber />\r\n            <Email />\r\n            <Username />\r\n            <AddressBook />\r\n          </Row>\r\n        </div>\r\n      </AccountLayout>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;;AAce,SAAS;IACtB,MAAM,YAAY;QAChB,SAAS;QACT,OAAO;QACP,aAAa;QACb,gBAAgB;QAChB,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,eAAe;QACf,qBAAqB;IACvB;IAEA,qBACE;kBACE,cAAA,6LAAC,kIAAA,CAAA,UAAa;;8BACZ,6LAAC,iIAAA,CAAA,UAAQ;oBAAC,OAAO;;;;;;8BACjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,2JAAA,CAAA,UAAc;4BAAC,OAAM;;;;;;sCACtB,6LAAC,qLAAA,CAAA,MAAG;;8CACF,6LAAC,iLAAA,CAAA,UAAmB;;;;;8CACpB,6LAAC,kLAAA,CAAA,UAAoB;;;;;8CACrB,6LAAC,yKAAA,CAAA,UAAW;;;;;8CACZ,6LAAC,mKAAA,CAAA,UAAK;;;;;8CACN,6LAAC,sKAAA,CAAA,UAAQ;;;;;8CACT,6LAAC,yKAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;;;;;;;;AAMxB;KA/BwB", "debugId": null}}, {"offset": {"line": 10045, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}